import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import 'accessibility_helpers.dart';

/// Apple-style button widget with glass morphism effect and smooth animations
class AppleStyleButton extends StatefulWidget {
  final Widget child;
  final VoidCallback? onTap;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double? size;
  final EdgeInsets? padding;
  final bool isCircular;
  final bool isActive;
  final String? tooltip;
  final Duration animationDuration;

  const AppleStyleButton({
    super.key,
    required this.child,
    this.onTap,
    this.backgroundColor,
    this.foregroundColor,
    this.size,
    this.padding,
    this.isCircular = true,
    this.isActive = false,
    this.tooltip,
    this.animationDuration = const Duration(milliseconds: 150),
  });

  @override
  State<AppleStyleButton> createState() => _AppleStyleButtonState();
}

class _AppleStyleButtonState extends State<AppleStyleButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: AppTheme.curveEaseOut,
    ));

    _opacityAnimation = Tween<double>(
      begin: 1.0,
      end: 0.8,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: AppTheme.curveEaseOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    setState(() => _isPressed = true);
    _animationController.forward();
    AppTheme.lightHaptic();
  }

  void _handleTapUp(TapUpDetails details) {
    setState(() => _isPressed = false);
    _animationController.reverse();
  }

  void _handleTapCancel() {
    setState(() => _isPressed = false);
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    // Ensure minimum touch target size for accessibility
    final size = (widget.size ?? AppTheme.preferredTouchTarget)
        .clamp(AccessibilityHelpers.minTouchTarget, double.infinity);
    final backgroundColor = widget.backgroundColor ??
        (widget.isActive ? AppTheme.accentBlue : AppTheme.darkGlassBackground);
    final foregroundColor = widget.foregroundColor ?? AppTheme.primaryWhite;

    Widget button = AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Opacity(
            opacity: _opacityAnimation.value,
            child: Container(
              width: size,
              height: size,
              padding: widget.padding ?? const EdgeInsets.all(AppTheme.spacing12),
              decoration: widget.isCircular
                  ? BoxDecoration(
                      color: backgroundColor,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: AppTheme.primaryWhite.withOpacity(0.2),
                        width: 0.5,
                      ),
                      boxShadow: _isPressed ? [] : AppTheme.shadowSmall,
                    )
                  : BoxDecoration(
                      color: backgroundColor,
                      borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
                      border: Border.all(
                        color: AppTheme.primaryWhite.withOpacity(0.2),
                        width: 0.5,
                      ),
                      boxShadow: _isPressed ? [] : AppTheme.shadowSmall,
                    ),
              child: IconTheme(
                data: IconThemeData(
                  color: foregroundColor,
                  size: 24,
                ),
                child: DefaultTextStyle(
                  style: AppTheme.labelMedium.copyWith(color: foregroundColor),
                  child: widget.child,
                ),
              ),
            ),
          ),
        );
      },
    );

    button = GestureDetector(
      onTapDown: _handleTapDown,
      onTapUp: _handleTapUp,
      onTapCancel: _handleTapCancel,
      onTap: widget.onTap,
      child: button,
    );

    // Add accessibility semantics
    button = Semantics(
      button: true,
      enabled: widget.onTap != null,
      label: widget.tooltip,
      child: button,
    );

    if (widget.tooltip != null) {
      button = Tooltip(
        message: widget.tooltip!,
        child: button,
      );
    }

    return button;
  }
}

/// Apple-style toggle button with smooth state transitions
class AppleStyleToggleButton extends StatefulWidget {
  final Widget activeChild;
  final Widget inactiveChild;
  final bool isActive;
  final ValueChanged<bool>? onChanged;
  final Color? activeColor;
  final Color? inactiveColor;
  final double? size;
  final String? tooltip;

  const AppleStyleToggleButton({
    super.key,
    required this.activeChild,
    required this.inactiveChild,
    required this.isActive,
    this.onChanged,
    this.activeColor,
    this.inactiveColor,
    this.size,
    this.tooltip,
  });

  @override
  State<AppleStyleToggleButton> createState() => _AppleStyleToggleButtonState();
}

class _AppleStyleToggleButtonState extends State<AppleStyleToggleButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _colorAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: AppTheme.animationMedium,
      vsync: this,
    );

    _colorAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: AppTheme.curveEaseInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: AppTheme.curveSpring,
    ));

    if (widget.isActive) {
      _animationController.value = 1.0;
    }
  }

  @override
  void didUpdateWidget(AppleStyleToggleButton oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isActive != oldWidget.isActive) {
      if (widget.isActive) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTap() {
    AppTheme.mediumHaptic();
    widget.onChanged?.call(!widget.isActive);
  }

  @override
  Widget build(BuildContext context) {
    final activeColor = widget.activeColor ?? AppTheme.accentBlue;
    final inactiveColor = widget.inactiveColor ?? AppTheme.darkGlassBackground;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        final backgroundColor = Color.lerp(
          inactiveColor,
          activeColor,
          _colorAnimation.value,
        )!;

        return Transform.scale(
          scale: _scaleAnimation.value,
          child: AppleStyleButton(
            onTap: _handleTap,
            backgroundColor: backgroundColor,
            size: widget.size,
            tooltip: widget.tooltip,
            child: AnimatedSwitcher(
              duration: AppTheme.animationFast,
              child: widget.isActive ? widget.activeChild : widget.inactiveChild,
            ),
          ),
        );
      },
    );
  }
}

/// Apple-style floating action button with glass morphism
class AppleStyleFloatingButton extends StatelessWidget {
  final Widget child;
  final VoidCallback? onTap;
  final Color? backgroundColor;
  final double size;
  final String? tooltip;

  const AppleStyleFloatingButton({
    super.key,
    required this.child,
    this.onTap,
    this.backgroundColor,
    this.size = 64.0,
    this.tooltip,
  });

  @override
  Widget build(BuildContext context) {
    return AppleStyleButton(
      onTap: onTap,
      backgroundColor: backgroundColor ?? AppTheme.glassBackgroundStrong,
      size: size,
      padding: const EdgeInsets.all(AppTheme.spacing16),
      tooltip: tooltip,
      child: child,
    );
  }
}
