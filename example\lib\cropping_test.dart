import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:path_provider/path_provider.dart';

/// Test widget to verify image cropping functionality
class CroppingTest extends StatefulWidget {
  const CroppingTest({super.key});

  @override
  State<CroppingTest> createState() => _CroppingTestState();
}

class _CroppingTestState extends State<CroppingTest> {
  final GlobalKey _repaintBoundaryKey = GlobalKey();
  String? _originalImagePath;
  String? _croppedImagePath;
  bool _isCapturing = false;

  /// Calculate the preview area bounds within the captured image
  Rect? _calculatePreviewBounds(Size capturedImageSize) {
    try {
      // Get the screen size
      final screenSize = MediaQuery.of(context).size;
      final screenPadding = MediaQuery.of(context).padding;

      // For this test, we know the exact layout:
      // - Top controls: 80px (simulated)
      // - Bottom controls: 200px (simulated)

      final topControlsHeight = screenPadding.top + 80; // Status bar + simulated top controls
      final bottomControlsHeight = 200; // Simulated bottom controls

      // Available preview area
      final availableHeight = screenSize.height - topControlsHeight - bottomControlsHeight;
      final availableWidth = screenSize.width;

      // Ensure we have a valid preview area
      if (availableHeight <= 0 || availableWidth <= 0) {
        debugPrint('Invalid preview area: width=$availableWidth, height=$availableHeight');
        return null;
      }

      // Calculate preview bounds (centered in available area)
      final previewTop = topControlsHeight;
      final previewLeft = 0.0;
      final previewWidth = availableWidth;
      final previewHeight = availableHeight;

      // Convert to captured image coordinates (accounting for pixel ratio)
      const pixelRatio = 2.0;
      final scaledTop = previewTop * pixelRatio;
      final scaledLeft = previewLeft * pixelRatio;
      final scaledWidth = previewWidth * pixelRatio;
      final scaledHeight = previewHeight * pixelRatio;

      debugPrint('Test Preview bounds:');
      debugPrint('  - Screen: ${screenSize.width}x${screenSize.height}');
      debugPrint('  - Top controls: $topControlsHeight');
      debugPrint('  - Bottom controls: $bottomControlsHeight');
      debugPrint('  - Available preview: ${previewWidth}x$previewHeight');
      debugPrint('  - Scaled bounds: top=$scaledTop, left=$scaledLeft, width=$scaledWidth, height=$scaledHeight');

      return Rect.fromLTWH(scaledLeft, scaledTop, scaledWidth, scaledHeight);
    } catch (e) {
      debugPrint('Error calculating preview bounds: $e');
      return null;
    }
  }

  /// Crop the captured image to show only the preview area
  Future<Uint8List?> _cropImageToPreview(Uint8List imageBytes) async {
    try {
      // Decode the image
      final ui.Codec codec = await ui.instantiateImageCodec(imageBytes);
      final ui.FrameInfo frameInfo = await codec.getNextFrame();
      final ui.Image originalImage = frameInfo.image;
      
      final imageSize = Size(originalImage.width.toDouble(), originalImage.height.toDouble());
      final previewBounds = _calculatePreviewBounds(imageSize);
      
      if (previewBounds == null) {
        debugPrint('Could not calculate preview bounds, returning original image');
        return imageBytes;
      }
      
      // Create a new canvas for the cropped image
      final recorder = ui.PictureRecorder();
      final canvas = Canvas(recorder);
      
      // Draw only the preview area
      canvas.drawImageRect(
        originalImage,
        previewBounds,
        Rect.fromLTWH(0, 0, previewBounds.width, previewBounds.height),
        Paint(),
      );
      
      // Convert to image
      final picture = recorder.endRecording();
      final croppedImage = await picture.toImage(
        previewBounds.width.toInt(),
        previewBounds.height.toInt(),
      );
      
      // Convert to bytes
      final ByteData? byteData = await croppedImage.toByteData(format: ui.ImageByteFormat.png);
      
      if (byteData == null) {
        debugPrint('Failed to convert cropped image to bytes');
        return imageBytes; // Return original if cropping fails
      }
      
      debugPrint('Successfully cropped image to preview area: ${byteData.lengthInBytes} bytes');
      return byteData.buffer.asUint8List();
    } catch (e) {
      debugPrint('Error cropping image: $e');
      return imageBytes; // Return original if cropping fails
    }
  }

  /// Capture and crop the widget
  Future<void> handleCapture() async {
    if (_isCapturing) return;

    setState(() {
      _isCapturing = true;
    });

    try {
      await Future.delayed(const Duration(milliseconds: 100));
      
      RenderRepaintBoundary? boundary = _repaintBoundaryKey.currentContext
          ?.findRenderObject() as RenderRepaintBoundary?;
      
      if (boundary == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('RepaintBoundary not found')),
        );
        return;
      }

      // Capture the boundary as an image
      ui.Image image = await boundary.toImage(pixelRatio: 2.0);
      ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      
      if (byteData == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to capture image')),
        );
        return;
      }

      final originalBytes = byteData.buffer.asUint8List();
      
      // Save original image
      final originalPath = await _saveImage(originalBytes, 'original');
      
      // Crop the image
      final croppedBytes = await _cropImageToPreview(originalBytes);
      
      if (croppedBytes != null) {
        // Save cropped image
        final croppedPath = await _saveImage(croppedBytes, 'cropped');
        
        setState(() {
          _originalImagePath = originalPath;
          _croppedImagePath = croppedPath;
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Images captured and cropped successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to crop image')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: $e')),
      );
    } finally {
      setState(() {
        _isCapturing = false;
      });
    }
  }

  /// Save image to file
  Future<String?> _saveImage(Uint8List bytes, String prefix) async {
    try {
      final Directory extDir = await getTemporaryDirectory();
      final testDir = await Directory(
        '${extDir.path}/cropping_test',
      ).create(recursive: true);
      
      final String filePath = 
          '${testDir.path}/${prefix}_${DateTime.now().millisecondsSinceEpoch}.png';
      
      final file = File(filePath);
      await file.writeAsBytes(bytes);
      
      debugPrint('$prefix image saved: $filePath');
      return filePath;
    } catch (e) {
      debugPrint('Error saving $prefix image: $e');
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Cropping Test'),
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
      ),
      body: RepaintBoundary(
        key: _repaintBoundaryKey,
        child: Column(
          children: [
            // Simulated top controls
            Container(
              height: 80,
              color: Colors.black.withOpacity(0.8),
              child: const Center(
                child: Text(
                  'TOP CONTROLS (NOT CAPTURED)',
                  style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                ),
              ),
            ),
            
            // Simulated camera preview area
            Expanded(
              child: Container(
                width: double.infinity,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.blue, Colors.purple, Colors.pink],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
                child: const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.camera_alt,
                        size: 100,
                        color: Colors.white,
                      ),
                      SizedBox(height: 20),
                      Text(
                        'CAMERA PREVIEW AREA',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      Text(
                        '(THIS SHOULD BE CAPTURED)',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.white70,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            
            // Simulated bottom controls
            Container(
              height: 200,
              color: Colors.black.withOpacity(0.8),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text(
                    'BOTTOM CONTROLS (NOT CAPTURED)',
                    style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 20),
                  ElevatedButton(
                    onPressed: _isCapturing ? null : handleCapture,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.deepPurple,
                      padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 15),
                    ),
                    child: _isCapturing
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : const Text(
                            'Test Capture & Crop',
                            style: TextStyle(fontSize: 16, color: Colors.white),
                          ),
                  ),
                  if (_originalImagePath != null && _croppedImagePath != null)
                    Padding(
                      padding: const EdgeInsets.only(top: 10),
                      child: Text(
                        'Images saved successfully!',
                        style: TextStyle(fontSize: 12, color: Colors.green[300]),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
