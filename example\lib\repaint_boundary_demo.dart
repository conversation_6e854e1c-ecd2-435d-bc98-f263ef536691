import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:path_provider/path_provider.dart';

/// Demo showing RepaintBoundary capture with simulated camera effects
class RepaintBoundaryDemo extends StatefulWidget {
  const RepaintBoundaryDemo({super.key});

  @override
  State<RepaintBoundaryDemo> createState() => _RepaintBoundaryDemoState();
}

class _RepaintBoundaryDemoState extends State<RepaintBoundaryDemo>
    with TickerProviderStateMixin {
  final GlobalKey _repaintBoundaryKey = GlobalKey();
  String? _savedImagePath;
  bool _isCapturing = false;
  
  // Simulated camera effects
  double _shutterSpeed = -1.0; // -1 = auto, positive = manual
  double _blurIntensity = 0.0;
  bool _motionBlurEnabled = false;
  
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();
    
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_animationController);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// Capture the RepaintBoundary as an image
  Future<Uint8List?> captureWidget() async {
    try {
      await Future.delayed(const Duration(milliseconds: 100));
      
      RenderRepaintBoundary? boundary = _repaintBoundaryKey.currentContext
          ?.findRenderObject() as RenderRepaintBoundary?;
      
      if (boundary == null) {
        debugPrint('RepaintBoundary not found');
        return null;
      }

      ui.Image image = await boundary.toImage(pixelRatio: 2.0);
      ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      
      if (byteData == null) {
        debugPrint('Failed to convert image to bytes');
        return null;
      }

      return byteData.buffer.asUint8List();
    } catch (e) {
      debugPrint('Error capturing widget: $e');
      return null;
    }
  }

  /// Save the captured image bytes to a file
  Future<String?> saveImage(Uint8List bytes) async {
    try {
      final Directory extDir = await getTemporaryDirectory();
      final testDir = await Directory(
        '${extDir.path}/repaint_demo',
      ).create(recursive: true);
      
      final String filePath = 
          '${testDir.path}/demo_${DateTime.now().millisecondsSinceEpoch}.png';
      
      final file = File(filePath);
      await file.writeAsBytes(bytes);
      
      debugPrint('Demo image saved: $filePath');
      return filePath;
    } catch (e) {
      debugPrint('Error saving demo image: $e');
      return null;
    }
  }

  /// Handle capture with effects logic (similar to main app)
  Future<void> handleCapture() async {
    if (_isCapturing) return;

    setState(() {
      _isCapturing = true;
    });

    try {
      // Check if effects are active (similar to main app logic)
      final hasShutterEffects = _shutterSpeed > 0;
      final hasBlurEffects = _blurIntensity > 0;
      
      if (hasShutterEffects || hasBlurEffects) {
        debugPrint('Effects detected - using RepaintBoundary capture');
        debugPrint('Shutter speed: $_shutterSpeed, Blur: $_blurIntensity');
        
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Capturing with effects...'),
            duration: Duration(milliseconds: 1000),
          ),
        );
        
        final imageBytes = await captureWidget();
        if (imageBytes != null) {
          final savedPath = await saveImage(imageBytes);
          if (savedPath != null) {
            setState(() {
              _savedImagePath = savedPath;
            });
            
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Photo captured with effects!'),
                backgroundColor: Colors.green,
                duration: Duration(milliseconds: 1500),
              ),
            );
            return;
          }
        }
        
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Effects capture failed'),
            backgroundColor: Colors.orange,
            duration: Duration(milliseconds: 1500),
          ),
        );
      } else {
        // Simulate normal capture
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Using normal capture (no effects)'),
            backgroundColor: Colors.blue,
            duration: Duration(milliseconds: 1500),
          ),
        );
      }
    } finally {
      setState(() {
        _isCapturing = false;
      });
    }
  }

  Widget _buildSimulatedCameraPreview() {
    return RepaintBoundary(
      key: _repaintBoundaryKey,
      child: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.deepPurple, Colors.blue, Colors.teal],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Stack(
          children: [
            // Simulated camera content
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.camera_alt,
                    size: 80,
                    color: Colors.white,
                  ),
                  const SizedBox(height: 20),
                  const Text(
                    'Simulated Camera Preview',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 10),
                  Text(
                    'Shutter: ${_shutterSpeed == -1 ? "AUTO" : "${_shutterSpeed}s"}',
                    style: const TextStyle(color: Colors.white70),
                  ),
                  Text(
                    'Blur: ${_blurIntensity.toStringAsFixed(1)}',
                    style: const TextStyle(color: Colors.white70),
                  ),
                ],
              ),
            ),
            
            // Simulated motion blur effect
            if (_motionBlurEnabled && _shutterSpeed > 0)
              AnimatedBuilder(
                animation: _animation,
                builder: (context, child) {
                  return Positioned(
                    left: 50 + (_animation.value * 200),
                    top: 200,
                    child: Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        color: Colors.yellow.withOpacity(0.8),
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.yellow.withOpacity(0.3),
                            blurRadius: _shutterSpeed * 5,
                            spreadRadius: _shutterSpeed * 2,
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            
            // Simulated blur overlay
            if (_blurIntensity > 0)
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(_blurIntensity * 0.1),
                  ),
                  child: BackdropFilter(
                    filter: ui.ImageFilter.blur(
                      sigmaX: _blurIntensity,
                      sigmaY: _blurIntensity,
                    ),
                    child: Container(
                      color: Colors.transparent,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('RepaintBoundary Demo'),
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          Expanded(
            flex: 3,
            child: _buildSimulatedCameraPreview(),
          ),
          
          // Controls
          Expanded(
            flex: 2,
            child: Container(
              padding: const EdgeInsets.all(16),
              color: Colors.grey[900],
              child: Column(
                children: [
                  // Shutter Speed Control
                  Row(
                    children: [
                      const Text('Shutter Speed:', style: TextStyle(color: Colors.white)),
                      Expanded(
                        child: Slider(
                          value: _shutterSpeed == -1 ? 0 : _shutterSpeed,
                          min: 0,
                          max: 2,
                          divisions: 20,
                          label: _shutterSpeed == -1 ? 'AUTO' : '${_shutterSpeed.toStringAsFixed(1)}s',
                          onChanged: (value) {
                            setState(() {
                              _shutterSpeed = value == 0 ? -1 : value;
                              _motionBlurEnabled = _shutterSpeed > 0;
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                  
                  // Blur Control
                  Row(
                    children: [
                      const Text('Blur:', style: TextStyle(color: Colors.white)),
                      Expanded(
                        child: Slider(
                          value: _blurIntensity,
                          min: 0,
                          max: 10,
                          divisions: 20,
                          label: _blurIntensity.toStringAsFixed(1),
                          onChanged: (value) {
                            setState(() {
                              _blurIntensity = value;
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 20),
                  
                  // Capture Button
                  ElevatedButton(
                    onPressed: _isCapturing ? null : handleCapture,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.deepPurple,
                      padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 15),
                    ),
                    child: _isCapturing
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : const Text(
                            'Capture Photo',
                            style: TextStyle(fontSize: 16, color: Colors.white),
                          ),
                  ),
                  
                  if (_savedImagePath != null)
                    Padding(
                      padding: const EdgeInsets.only(top: 10),
                      child: Text(
                        'Last capture: ${_savedImagePath!.split('/').last}',
                        style: const TextStyle(fontSize: 12, color: Colors.grey),
                      ),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
