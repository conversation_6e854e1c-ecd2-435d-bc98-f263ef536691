import 'package:camerawesome/camerawesome_plugin.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';

/// White Balance preset modes with their corresponding Kelvin temperatures
enum WhiteBalancePreset {
  cloudy(6500, Icons.cloud, 'Cloudy'),
  indoor(3000, Icons.home, 'Indoor'),
  fluorescent(4200, Icons.lightbulb, 'Fluorescent'),
  daylight(5500, Icons.wb_sunny, 'Daylight'),
  sunny(7000, Icons.sunny, 'Sunny'),
  lock(0, Icons.lock, 'Lock');

  const WhiteBalancePreset(this.kelvin, this.icon, this.label);
  
  final int kelvin;
  final IconData icon;
  final String label;
}

/// A professional white balance control panel for camera applications
/// 
/// Features:
/// - Auto/Manual white balance toggle
/// - Preset mode buttons (Cloudy, Indoor, Fluorescent, Daylight, Sunny, Lock)
/// - Kelvin temperature slider (2000K-9000K) with gradient background
/// - Real-time temperature display
/// - Smooth animations and haptic feedback
/// - Consistent styling with existing CamerAwesome selectors
class AwesomeWhiteBalanceSelector extends StatefulWidget {
  final CameraState state;
  final bool showResetButton;
  final Color? sliderActiveColor;
  final Color? sliderInactiveColor;
  final Color? textColor;
  final ValueNotifier<bool>? visibilityNotifier;
  final bool showButton;
  final EdgeInsets padding;
  final bool showLabel;

  const AwesomeWhiteBalanceSelector({
    super.key,
    required this.state,
    this.showResetButton = true,
    this.sliderActiveColor,
    this.sliderInactiveColor,
    this.textColor,
    this.visibilityNotifier,
    this.showButton = true,
    this.padding = const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
    this.showLabel = true,
  });

  @override
  State<AwesomeWhiteBalanceSelector> createState() => _AwesomeWhiteBalanceSelectorState();
}

class _AwesomeWhiteBalanceSelectorState extends State<AwesomeWhiteBalanceSelector> {
  late ValueNotifier<bool> _internalVisibilityNotifier;
  double _currentKelvin = 5500.0; // Default daylight temperature
  bool _isAutoMode = true;
  WhiteBalancePreset? _selectedPreset;
  Timer? _debounceTimer;
  bool _isLocked = false;

  @override
  void initState() {
    super.initState();
    _internalVisibilityNotifier = widget.visibilityNotifier ?? ValueNotifier(false);
    _initializeWhiteBalance();
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    if (widget.visibilityNotifier == null) {
      _internalVisibilityNotifier.dispose();
    }
    super.dispose();
  }

  void _initializeWhiteBalance() {
    // Listen to white balance changes from the camera state
    widget.state.whiteBalance$.listen((kelvin) {
      if (mounted && kelvin != _currentKelvin) {
        setState(() {
          _currentKelvin = kelvin == -1.0 ? 5500.0 : kelvin;
        });
      }
    });

    widget.state.whiteBalanceAuto$.listen((isAuto) {
      if (mounted && isAuto != _isAutoMode) {
        setState(() {
          _isAutoMode = isAuto;
          if (isAuto) {
            _selectedPreset = null;
            _isLocked = false;
          }
        });
      }
    });
  }

  void _onKelvinChanged(double value) {
    if (_isLocked) return;
    
    setState(() {
      _currentKelvin = value;
      _isAutoMode = false;
      _selectedPreset = null;
    });

    // Debounce the actual state update
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 100), () {
      if (mounted) {
        widget.state.setWhiteBalance(value);
        widget.state.setWhiteBalanceAuto(false);
      }
    });

    HapticFeedback.selectionClick();
  }

  void _onPresetSelected(WhiteBalancePreset preset) {
    if (preset == WhiteBalancePreset.lock) {
      setState(() {
        _isLocked = !_isLocked;
        _selectedPreset = _isLocked ? preset : null;
      });
      HapticFeedback.mediumImpact();
      return;
    }

    setState(() {
      _selectedPreset = preset;
      _currentKelvin = preset.kelvin.toDouble();
      _isAutoMode = false;
      _isLocked = false;
    });

    widget.state.setWhiteBalance(preset.kelvin.toDouble());
    widget.state.setWhiteBalanceAuto(false);
    HapticFeedback.lightImpact();
  }

  void _toggleAutoMode() {
    setState(() {
      _isAutoMode = !_isAutoMode;
      if (_isAutoMode) {
        _selectedPreset = null;
        _isLocked = false;
      }
    });

    widget.state.setWhiteBalanceAuto(_isAutoMode);
    if (_isAutoMode) {
      widget.state.setWhiteBalance(-1.0);
    }
    HapticFeedback.mediumImpact();
  }

  void _toggleVisibility() {
    _internalVisibilityNotifier.value = !_internalVisibilityNotifier.value;
    HapticFeedback.lightImpact();
  }

  String _formatKelvin(double kelvin) {
    return "${kelvin.round()}K";
  }

  @override
  Widget build(BuildContext context) {
    final theme = AwesomeThemeProvider.of(context).theme;
    final buttonTheme = theme.buttonTheme;

    return StreamBuilder<SensorConfig>(
      stream: widget.state.sensorConfig$,
      builder: (context, snapshot) {

        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // White balance toggle button (conditionally rendered)
            if (widget.showButton)
              AwesomeOrientedWidget(
                rotateWithDevice: buttonTheme.rotateWithCamera,
                child: buttonTheme.buttonBuilder(
                  AwesomeCircleWidget.icon(
                    icon: Icons.wb_auto,
                    theme: theme,
                  ),
                  _toggleVisibility,
                ),
              ),
            
            // White balance control panel
            ValueListenableBuilder<bool>(
              valueListenable: _internalVisibilityNotifier,
              builder: (context, isVisible, child) {
                return AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeInOut,
                  height: isVisible ? 160 : 0,
                  child: isVisible ? _buildWhiteBalancePanel(theme) : null,
                );
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildWhiteBalancePanel(AwesomeTheme theme) {
    return Container(
      padding: widget.padding,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Label (conditionally rendered)
          if (widget.showLabel)
            Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: Text(
                _isAutoMode ? "Auto WB" : _formatKelvin(_currentKelvin),
                style: TextStyle(
                  color: widget.textColor ?? Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),

          // Preset buttons row
          _buildPresetButtons(theme),

          const SizedBox(height: 16),

          // Kelvin slider with AUTO button
          _buildKelvinSlider(theme),
        ],
      ),
    );
  }

  Widget _buildPresetButtons(AwesomeTheme theme) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: WhiteBalancePreset.values.map((preset) {
        final isSelected = _selectedPreset == preset;
        final isDisabled = _isAutoMode && preset != WhiteBalancePreset.lock;

        return Opacity(
          opacity: isDisabled ? 0.5 : 1.0,
          child: GestureDetector(
            onTap: isDisabled ? null : () => _onPresetSelected(preset),
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: isSelected
                    ? theme.buttonTheme.foregroundColor.withOpacity(0.2)
                    : Colors.transparent,
                border: Border.all(
                  color: isSelected
                      ? theme.buttonTheme.foregroundColor
                      : theme.buttonTheme.foregroundColor.withOpacity(0.3),
                  width: isSelected ? 2 : 1,
                ),
                borderRadius: BorderRadius.circular(8),
                boxShadow: isSelected ? [
                  BoxShadow(
                    color: theme.buttonTheme.foregroundColor.withOpacity(0.3),
                    blurRadius: 8,
                    spreadRadius: 0,
                  ),
                ] : null,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    preset.icon,
                    size: 16,
                    color: isSelected
                        ? theme.buttonTheme.foregroundColor
                        : theme.buttonTheme.foregroundColor.withOpacity(0.7),
                  ),
                  if (preset != WhiteBalancePreset.lock)
                    Text(
                      "${preset.kelvin}K",
                      style: TextStyle(
                        fontSize: 8,
                        color: isSelected
                            ? theme.buttonTheme.foregroundColor
                            : theme.buttonTheme.foregroundColor.withOpacity(0.7),
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildKelvinSlider(AwesomeTheme theme) {
    return Row(
      children: [
        // Kelvin slider with gradient background
        Expanded(
          child: Stack(
            children: [
              // Gradient background track
              Container(
                height: 4,
                margin: const EdgeInsets.symmetric(horizontal: 24),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(2),
                  gradient: const LinearGradient(
                    colors: [
                      Color(0xFFFF8C00), // Warm orange (2000K)
                      Color(0xFFFFB347), // Light orange (3000K)
                      Color(0xFFFFF8DC), // Cornsilk (4000K)
                      Color(0xFFFFFFFF), // White (5500K)
                      Color(0xFFE6F3FF), // Light blue (7000K)
                      Color(0xFFB0E0E6), // Powder blue (9000K)
                    ],
                  ),
                ),
              ),

              // Slider
              SliderTheme(
                data: SliderTheme.of(context).copyWith(
                  activeTrackColor: Colors.transparent,
                  inactiveTrackColor: Colors.transparent,
                  thumbColor: widget.sliderActiveColor ?? Colors.white,
                  overlayColor: (widget.sliderActiveColor ?? Colors.white).withOpacity(0.2),
                  trackHeight: 4,
                  thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 10),
                ),
                child: Slider(
                  value: _currentKelvin,
                  min: 2000.0,
                  max: 9000.0,
                  divisions: 140, // 50K increments
                  onChanged: _isAutoMode || _isLocked ? null : _onKelvinChanged,
                ),
              ),

              // Temperature tooltip
              if (!_isAutoMode && !_isLocked)
                Positioned(
                  left: 24 + ((_currentKelvin - 2000) / 7000) * (MediaQuery.of(context).size.width - 120),
                  top: -30,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.black87,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      _formatKelvin(_currentKelvin),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),

        const SizedBox(width: 12),

        // AUTO button
        GestureDetector(
          onTap: _toggleAutoMode,
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            width: 50,
            height: 32,
            decoration: BoxDecoration(
              color: _isAutoMode
                  ? Colors.blue.withOpacity(0.8)
                  : Colors.transparent,
              border: Border.all(
                color: _isAutoMode
                    ? Colors.blue
                    : theme.buttonTheme.foregroundColor.withOpacity(0.5),
                width: 1,
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: _isAutoMode ? [
                BoxShadow(
                  color: Colors.blue.withOpacity(0.3),
                  blurRadius: 8,
                  spreadRadius: 0,
                ),
              ] : null,
            ),
            child: Center(
              child: Text(
                "AUTO",
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                  color: _isAutoMode
                      ? Colors.white
                      : theme.buttonTheme.foregroundColor.withOpacity(0.7),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

/// A compact version of the white balance selector without toggle button
/// Perfect for integration into bottom action bars or side panels
class AwesomeCompactWhiteBalanceSelector extends StatelessWidget {
  final CameraState state;
  final Color? sliderActiveColor;
  final Color? sliderInactiveColor;
  final Color? textColor;
  final EdgeInsets padding;

  const AwesomeCompactWhiteBalanceSelector({
    super.key,
    required this.state,
    this.sliderActiveColor,
    this.sliderInactiveColor,
    this.textColor,
    this.padding = const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
  });

  @override
  Widget build(BuildContext context) {
    return AwesomeWhiteBalanceSelector(
      state: state,
      sliderActiveColor: sliderActiveColor,
      sliderInactiveColor: sliderInactiveColor,
      textColor: textColor,
      padding: padding,
      showButton: false,
      showLabel: false,
      visibilityNotifier: ValueNotifier(true), // Always visible
    );
  }
}
