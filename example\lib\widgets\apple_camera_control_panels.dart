import 'package:flutter/material.dart';
import 'package:camerawesome/camerawesome_plugin.dart';
import '../theme/app_theme.dart';
import 'apple_style_control_panel.dart';
import 'apple_style_slider.dart';

/// Apple-style exposure control panel
class AppleExposureControlPanel extends StatelessWidget {
  final CameraState state;
  final bool isVisible;
  final VoidCallback? onClose;

  const AppleExposureControlPanel({
    super.key,
    required this.state,
    required this.isVisible,
    this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return AppleStyleControlPanel(
      title: 'Exposure',
      icon: const Icon(Icons.exposure),
      isVisible: isVisible,
      accentColor: AppTheme.exposureControlColor,
      height: 160,
      onToggle: onClose,
      children: [
        StreamBuilder<double>(
          stream: state.exposureOffset$,
          builder: (context, snapshot) {
            final currentExposure = snapshot.data ?? 0.0;
            return Column(
              children: [
                AppleStyleValueDisplay(
                  label: 'Exposure Compensation',
                  value: '${currentExposure >= 0 ? '+' : ''}${currentExposure.toStringAsFixed(1)} EV',
                  accentColor: AppTheme.exposureControlColor,
                  icon: Icons.exposure,
                ),
                const SizedBox(height: AppTheme.spacing16),
                AppleStyleSlider(
                  value: currentExposure,
                  min: -3.0,
                  max: 3.0,
                  activeColor: AppTheme.exposureControlColor,
                  showValue: false,
                  hapticFeedback: true,
                  onChanged: (value) {
                    state.setExposureOffset(value);
                  },
                ),
              ],
            );
          },
        ),
      ],
    );
  }
}

/// Apple-style shutter speed control panel
class AppleShutterSpeedControlPanel extends StatefulWidget {
  final CameraState state;
  final bool isVisible;
  final VoidCallback? onClose;

  const AppleShutterSpeedControlPanel({
    super.key,
    required this.state,
    required this.isVisible,
    this.onClose,
  });

  @override
  State<AppleShutterSpeedControlPanel> createState() => _AppleShutterSpeedControlPanelState();
}

class _AppleShutterSpeedControlPanelState extends State<AppleShutterSpeedControlPanel> {
  bool _isAutoMode = true;
  double _currentShutterSpeed = 1.0 / 60.0; // 1/60s default

  // Common shutter speed values (in seconds)
  final List<double> _shutterSpeedValues = [
    1.0 / 4000.0, // 1/4000s
    1.0 / 2000.0, // 1/2000s
    1.0 / 1000.0, // 1/1000s
    1.0 / 500.0,  // 1/500s
    1.0 / 250.0,  // 1/250s
    1.0 / 125.0,  // 1/125s
    1.0 / 60.0,   // 1/60s
    1.0 / 30.0,   // 1/30s
    1.0 / 15.0,   // 1/15s
    1.0 / 8.0,    // 1/8s
    1.0 / 4.0,    // 1/4s
    1.0 / 2.0,    // 1/2s
    1.0,          // 1s
    2.0,          // 2s
  ];

  final List<AppleStylePreset> _shutterSpeedPresets = [
    const AppleStylePreset(id: 'fast', label: 'FAST', value: 1.0 / 1000.0),
    const AppleStylePreset(id: 'normal', label: 'NORMAL', value: 1.0 / 60.0),
    const AppleStylePreset(id: 'slow', label: 'SLOW', value: 1.0 / 4.0),
  ];

  String? _selectedPreset;

  @override
  Widget build(BuildContext context) {
    return AppleStyleControlPanel(
      title: 'Shutter Speed',
      icon: const Icon(Icons.shutter_speed),
      isVisible: widget.isVisible,
      accentColor: AppTheme.shutterSpeedControlColor,
      height: 220,
      onToggle: widget.onClose,
      children: [
        // Auto/Manual toggle
        AppleStyleAutoToggle(
          isAuto: _isAutoMode,
          onChanged: (isAuto) {
            setState(() {
              _isAutoMode = isAuto;
              if (isAuto) {
                _selectedPreset = null;
              }
            });
            // Apply to camera state
            if (isAuto) {
              // Set to auto mode (implementation depends on camera API)
              widget.state.setShutterSpeed(null);
            } else {
              widget.state.setShutterSpeed(_currentShutterSpeed);
            }
          },
          accentColor: AppTheme.shutterSpeedControlColor,
        ),
        
        if (!_isAutoMode) ...[
          const SizedBox(height: AppTheme.spacing16),
          
          // Preset buttons
          AppleStylePresetRow(
            presets: _shutterSpeedPresets,
            selectedPreset: _selectedPreset,
            accentColor: AppTheme.shutterSpeedControlColor,
            onPresetSelected: (presetId) {
              final preset = _shutterSpeedPresets.firstWhere((p) => p.id == presetId);
              setState(() {
                _selectedPreset = presetId;
                _currentShutterSpeed = preset.value as double;
              });
              widget.state.setShutterSpeed(_currentShutterSpeed);
            },
          ),
          
          const SizedBox(height: AppTheme.spacing16),
          
          // Value display
          AppleStyleValueDisplay(
            label: 'Shutter Speed',
            value: _formatShutterSpeed(_currentShutterSpeed),
            accentColor: AppTheme.shutterSpeedControlColor,
            icon: Icons.shutter_speed,
          ),
        ],
      ],
    );
  }

  String _formatShutterSpeed(double seconds) {
    if (seconds >= 1.0) {
      return '${seconds.toStringAsFixed(1)}s';
    } else {
      final fraction = (1.0 / seconds).round();
      return '1/${fraction}s';
    }
  }
}

/// Apple-style blur control panel
class AppleBlurControlPanel extends StatelessWidget {
  final PhotoCameraState state;
  final bool isVisible;
  final VoidCallback? onClose;

  const AppleBlurControlPanel({
    super.key,
    required this.state,
    required this.isVisible,
    this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return AppleStyleControlPanel(
      title: 'Portrait Blur',
      icon: const Icon(Icons.blur_on),
      isVisible: isVisible,
      accentColor: AppTheme.accentPurple,
      height: 160,
      onToggle: onClose,
      children: [
        StreamBuilder<double>(
          stream: state.blur$,
          builder: (context, snapshot) {
            final currentBlur = snapshot.data ?? 0.0;
            return Column(
              children: [
                AppleStyleValueDisplay(
                  label: 'Blur Intensity',
                  value: currentBlur == 0.0 ? 'Off' : '${(currentBlur * 100).round()}%',
                  accentColor: AppTheme.accentPurple,
                  icon: Icons.blur_on,
                ),
                const SizedBox(height: AppTheme.spacing16),
                AppleStyleSlider(
                  value: currentBlur,
                  min: 0.0,
                  max: 1.0,
                  activeColor: AppTheme.accentPurple,
                  showValue: false,
                  hapticFeedback: true,
                  onChanged: (value) {
                    state.setBlurIntensity(value);
                  },
                ),
              ],
            );
          },
        ),
      ],
    );
  }
}

/// Apple-style focus control panel
class AppleFocusControlPanel extends StatefulWidget {
  final CameraState state;
  final bool isVisible;
  final VoidCallback? onClose;

  const AppleFocusControlPanel({
    super.key,
    required this.state,
    required this.isVisible,
    this.onClose,
  });

  @override
  State<AppleFocusControlPanel> createState() => _AppleFocusControlPanelState();
}

class _AppleFocusControlPanelState extends State<AppleFocusControlPanel> {
  bool _isAutoFocus = true;
  double _manualFocusDistance = 0.5;

  @override
  Widget build(BuildContext context) {
    return AppleStyleControlPanel(
      title: 'Focus',
      icon: const Icon(Icons.center_focus_strong),
      isVisible: widget.isVisible,
      accentColor: AppTheme.focusControlColor,
      height: 180,
      onToggle: widget.onClose,
      children: [
        // Auto/Manual focus toggle
        AppleStyleAutoToggle(
          isAuto: _isAutoFocus,
          autoLabel: 'AF',
          manualLabel: 'MF',
          onChanged: (isAuto) {
            setState(() {
              _isAutoFocus = isAuto;
            });
            // Apply to camera state
            if (isAuto) {
              // Enable auto focus
              widget.state.setAutoFocus(true);
            } else {
              // Set manual focus
              widget.state.setAutoFocus(false);
              widget.state.setFocusDistance(_manualFocusDistance);
            }
          },
          accentColor: AppTheme.focusControlColor,
        ),
        
        if (!_isAutoFocus) ...[
          const SizedBox(height: AppTheme.spacing16),
          
          AppleStyleValueDisplay(
            label: 'Focus Distance',
            value: _manualFocusDistance == 0.0 
                ? 'Infinity' 
                : '${(_manualFocusDistance * 10).toStringAsFixed(1)}m',
            accentColor: AppTheme.focusControlColor,
            icon: Icons.center_focus_strong,
          ),
          
          const SizedBox(height: AppTheme.spacing16),
          
          AppleStyleSlider(
            value: _manualFocusDistance,
            min: 0.0,
            max: 1.0,
            activeColor: AppTheme.focusControlColor,
            showValue: false,
            hapticFeedback: true,
            onChanged: (value) {
              setState(() {
                _manualFocusDistance = value;
              });
              widget.state.setFocusDistance(value);
            },
          ),
        ],
      ],
    );
  }
}
