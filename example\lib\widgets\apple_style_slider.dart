import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../theme/app_theme.dart';

/// Apple-style slider with glass morphism and smooth animations
class AppleStyleSlider extends StatefulWidget {
  final double value;
  final double min;
  final double max;
  final ValueChanged<double>? onChanged;
  final ValueChanged<double>? onChangeStart;
  final ValueChanged<double>? onChangeEnd;
  final Color? activeColor;
  final Color? inactiveColor;
  final Color? thumbColor;
  final double height;
  final double thumbSize;
  final bool showValue;
  final String Function(double)? valueFormatter;
  final List<double>? divisions;
  final bool hapticFeedback;

  const AppleStyleSlider({
    super.key,
    required this.value,
    this.min = 0.0,
    this.max = 1.0,
    this.onChanged,
    this.onChangeStart,
    this.onChangeEnd,
    this.activeColor,
    this.inactiveColor,
    this.thumbColor,
    this.height = 6.0,
    this.thumbSize = 24.0,
    this.showValue = false,
    this.valueFormatter,
    this.divisions,
    this.hapticFeedback = true,
  });

  @override
  State<AppleStyleSlider> createState() => _AppleStyleSliderState();
}

class _AppleStyleSliderState extends State<AppleStyleSlider>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _thumbScaleAnimation;
  bool _isDragging = false;
  double? _lastHapticValue;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: AppTheme.animationFast,
      vsync: this,
    );

    _thumbScaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: AppTheme.curveEaseOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleChangeStart(double value) {
    setState(() => _isDragging = true);
    _animationController.forward();
    widget.onChangeStart?.call(value);
    if (widget.hapticFeedback) {
      AppTheme.lightHaptic();
    }
  }

  void _handleChange(double value) {
    widget.onChanged?.call(value);
    
    // Haptic feedback for divisions
    if (widget.hapticFeedback && widget.divisions != null) {
      final closestDivision = _findClosestDivision(value);
      if (_lastHapticValue != closestDivision) {
        AppTheme.selectionHaptic();
        _lastHapticValue = closestDivision;
      }
    }
  }

  void _handleChangeEnd(double value) {
    setState(() => _isDragging = false);
    _animationController.reverse();
    widget.onChangeEnd?.call(value);
    if (widget.hapticFeedback) {
      AppTheme.mediumHaptic();
    }
  }

  double _findClosestDivision(double value) {
    if (widget.divisions == null) return value;
    
    double closest = widget.divisions!.first;
    double minDistance = (value - closest).abs();
    
    for (final division in widget.divisions!) {
      final distance = (value - division).abs();
      if (distance < minDistance) {
        minDistance = distance;
        closest = division;
      }
    }
    
    return closest;
  }

  @override
  Widget build(BuildContext context) {
    final activeColor = widget.activeColor ?? AppTheme.accentBlue;
    final inactiveColor = widget.inactiveColor ?? AppTheme.systemGray3;
    final thumbColor = widget.thumbColor ?? AppTheme.primaryWhite;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.showValue) ...[
          Text(
            widget.valueFormatter?.call(widget.value) ?? 
                widget.value.toStringAsFixed(1),
            style: AppTheme.labelMedium,
          ),
          const SizedBox(height: AppTheme.spacing8),
        ],
        SizedBox(
          height: math.max(widget.height, widget.thumbSize) + AppTheme.spacing8,
          child: SliderTheme(
            data: SliderThemeData(
              trackHeight: widget.height,
              thumbShape: _AppleSliderThumbShape(
                thumbRadius: widget.thumbSize / 2,
                thumbColor: thumbColor,
                scaleAnimation: _thumbScaleAnimation,
              ),
              overlayShape: _AppleSliderOverlayShape(
                overlayRadius: widget.thumbSize,
              ),
              activeTrackColor: activeColor,
              inactiveTrackColor: inactiveColor,
              thumbColor: thumbColor,
              overlayColor: activeColor.withOpacity(0.2),
              trackShape: _AppleSliderTrackShape(),
              valueIndicatorShape: const PaddleSliderValueIndicatorShape(),
              valueIndicatorColor: activeColor,
              valueIndicatorTextStyle: AppTheme.labelSmall,
            ),
            child: AnimatedBuilder(
              animation: _thumbScaleAnimation,
              builder: (context, child) {
                return Slider(
                  value: widget.value.clamp(widget.min, widget.max),
                  min: widget.min,
                  max: widget.max,
                  divisions: widget.divisions?.length,
                  onChanged: widget.onChanged,
                  onChangeStart: _handleChangeStart,
                  onChangeEnd: _handleChangeEnd,
                );
              },
            ),
          ),
        ),
      ],
    );
  }
}

/// Custom slider thumb shape with glass morphism effect
class _AppleSliderThumbShape extends SliderComponentShape {
  final double thumbRadius;
  final Color thumbColor;
  final Animation<double> scaleAnimation;

  const _AppleSliderThumbShape({
    required this.thumbRadius,
    required this.thumbColor,
    required this.scaleAnimation,
  });

  @override
  Size getPreferredSize(bool isEnabled, bool isDiscrete) {
    return Size.fromRadius(thumbRadius);
  }

  @override
  void paint(
    PaintingContext context,
    Offset center, {
    required Animation<double> activationAnimation,
    required Animation<double> enableAnimation,
    required bool isDiscrete,
    required TextPainter labelPainter,
    required RenderBox parentBox,
    required SliderThemeData sliderTheme,
    required TextDirection textDirection,
    required double value,
    required double textScaleFactor,
    required Size sizeWithOverflow,
  }) {
    final Canvas canvas = context.canvas;
    final double radius = thumbRadius * scaleAnimation.value;

    // Draw shadow
    final shadowPaint = Paint()
      ..color = AppTheme.primaryBlack.withOpacity(0.2)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 4);
    canvas.drawCircle(center + const Offset(0, 2), radius, shadowPaint);

    // Draw thumb background with glass effect
    final backgroundPaint = Paint()
      ..color = thumbColor
      ..style = PaintingStyle.fill;
    canvas.drawCircle(center, radius, backgroundPaint);

    // Draw border
    final borderPaint = Paint()
      ..color = AppTheme.systemGray3
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;
    canvas.drawCircle(center, radius, borderPaint);

    // Draw inner highlight
    final highlightPaint = Paint()
      ..color = AppTheme.primaryWhite.withOpacity(0.3)
      ..style = PaintingStyle.fill;
    canvas.drawCircle(center - const Offset(0, 1), radius * 0.6, highlightPaint);
  }
}

/// Custom slider overlay shape
class _AppleSliderOverlayShape extends SliderComponentShape {
  final double overlayRadius;

  const _AppleSliderOverlayShape({required this.overlayRadius});

  @override
  Size getPreferredSize(bool isEnabled, bool isDiscrete) {
    return Size.fromRadius(overlayRadius);
  }

  @override
  void paint(
    PaintingContext context,
    Offset center, {
    required Animation<double> activationAnimation,
    required Animation<double> enableAnimation,
    required bool isDiscrete,
    required TextPainter labelPainter,
    required RenderBox parentBox,
    required SliderThemeData sliderTheme,
    required TextDirection textDirection,
    required double value,
    required double textScaleFactor,
    required Size sizeWithOverflow,
  }) {
    final Canvas canvas = context.canvas;
    final double radius = overlayRadius * activationAnimation.value;

    if (radius > 0) {
      final overlayPaint = Paint()
        ..color = sliderTheme.overlayColor!
        ..style = PaintingStyle.fill;
      canvas.drawCircle(center, radius, overlayPaint);
    }
  }
}

/// Custom slider track shape with rounded ends
class _AppleSliderTrackShape extends SliderTrackShape {
  @override
  Rect getPreferredRect({
    required RenderBox parentBox,
    Offset offset = Offset.zero,
    required SliderThemeData sliderTheme,
    bool isEnabled = false,
    bool isDiscrete = false,
  }) {
    final double trackHeight = sliderTheme.trackHeight!;
    final double trackLeft = offset.dx;
    final double trackTop = offset.dy + (parentBox.size.height - trackHeight) / 2;
    final double trackWidth = parentBox.size.width;
    return Rect.fromLTWH(trackLeft, trackTop, trackWidth, trackHeight);
  }

  @override
  void paint(
    PaintingContext context,
    Offset offset, {
    required RenderBox parentBox,
    required SliderThemeData sliderTheme,
    required Animation<double> enableAnimation,
    required TextDirection textDirection,
    required Offset thumbCenter,
    Offset? secondaryOffset,
    bool isDiscrete = false,
    bool isEnabled = false,
    double additionalActiveTrackHeight = 2,
  }) {
    final Rect trackRect = getPreferredRect(
      parentBox: parentBox,
      offset: offset,
      sliderTheme: sliderTheme,
      isEnabled: isEnabled,
      isDiscrete: isDiscrete,
    );

    final Canvas canvas = context.canvas;
    final double trackRadius = trackRect.height / 2;

    // Draw inactive track
    final inactiveTrackPaint = Paint()
      ..color = sliderTheme.inactiveTrackColor!
      ..style = PaintingStyle.fill;
    canvas.drawRRect(
      RRect.fromRectAndRadius(trackRect, Radius.circular(trackRadius)),
      inactiveTrackPaint,
    );

    // Draw active track
    final activeTrackRect = Rect.fromLTRB(
      trackRect.left,
      trackRect.top,
      thumbCenter.dx,
      trackRect.bottom,
    );

    if (activeTrackRect.width > 0) {
      final activeTrackPaint = Paint()
        ..color = sliderTheme.activeTrackColor!
        ..style = PaintingStyle.fill;
      canvas.drawRRect(
        RRect.fromRectAndRadius(activeTrackRect, Radius.circular(trackRadius)),
        activeTrackPaint,
      );
    }
  }
}
