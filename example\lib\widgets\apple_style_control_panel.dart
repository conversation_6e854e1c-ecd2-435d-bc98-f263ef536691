import 'package:flutter/material.dart';
import 'dart:ui';
import '../theme/app_theme.dart';
import 'apple_style_button.dart';
import 'apple_style_slider.dart';

/// Apple-style control panel with glass morphism background
class AppleStyleControlPanel extends StatefulWidget {
  final String title;
  final Widget? icon;
  final List<Widget> children;
  final bool isVisible;
  final VoidCallback? onToggle;
  final Color? accentColor;
  final EdgeInsets? padding;
  final double? height;
  final Duration animationDuration;

  const AppleStyleControlPanel({
    super.key,
    required this.title,
    this.icon,
    required this.children,
    this.isVisible = false,
    this.onToggle,
    this.accentColor,
    this.padding,
    this.height,
    this.animationDuration = const Duration(milliseconds: 300),
  });

  @override
  State<AppleStyleControlPanel> createState() => _AppleStyleControlPanelState();
}

class _AppleStyleControlPanelState extends State<AppleStyleControlPanel>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _heightAnimation;
  late Animation<double> _opacityAnimation;
  late Animation<double> _blurAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    _heightAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: AppTheme.curveEaseInOut,
    ));

    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.3, 1.0, curve: Curves.easeOut),
    ));

    _blurAnimation = Tween<double>(
      begin: 0.0,
      end: 10.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: AppTheme.curveEaseOut,
    ));

    if (widget.isVisible) {
      _animationController.value = 1.0;
    }
  }

  @override
  void didUpdateWidget(AppleStyleControlPanel oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isVisible != oldWidget.isVisible) {
      if (widget.isVisible) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final accentColor = widget.accentColor ?? AppTheme.accentBlue;
    final panelHeight = widget.height ?? 200.0;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return SizedBox(
          height: panelHeight * _heightAnimation.value,
          child: _heightAnimation.value > 0
              ? ClipRRect(
                  borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
                  child: BackdropFilter(
                    filter: ImageFilter.blur(
                      sigmaX: _blurAnimation.value,
                      sigmaY: _blurAnimation.value,
                    ),
                    child: Container(
                      decoration: BoxDecoration(
                        color: AppTheme.darkGlassBackgroundStrong,
                        borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
                        border: Border.all(
                          color: accentColor.withOpacity(0.3),
                          width: 1,
                        ),
                        boxShadow: AppTheme.shadowMedium,
                      ),
                      child: Opacity(
                        opacity: _opacityAnimation.value,
                        child: Padding(
                          padding: widget.padding ?? 
                              const EdgeInsets.all(AppTheme.spacing16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              _buildHeader(accentColor),
                              const SizedBox(height: AppTheme.spacing12),
                              Expanded(
                                child: SingleChildScrollView(
                                  child: Column(
                                    children: widget.children,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                )
              : const SizedBox.shrink(),
        );
      },
    );
  }

  Widget _buildHeader(Color accentColor) {
    return Row(
      children: [
        if (widget.icon != null) ...[
          IconTheme(
            data: IconThemeData(
              color: accentColor,
              size: 20,
            ),
            child: widget.icon!,
          ),
          const SizedBox(width: AppTheme.spacing8),
        ],
        Expanded(
          child: Text(
            widget.title,
            style: AppTheme.headlineSmall.copyWith(
              color: accentColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        if (widget.onToggle != null)
          AppleStyleButton(
            onTap: widget.onToggle,
            size: 32,
            backgroundColor: AppTheme.systemGray.withOpacity(0.3),
            child: const Icon(
              Icons.close,
              size: 16,
              color: AppTheme.systemGray,
            ),
          ),
      ],
    );
  }
}

/// Apple-style preset button row
class AppleStylePresetRow extends StatelessWidget {
  final List<AppleStylePreset> presets;
  final String? selectedPreset;
  final ValueChanged<String>? onPresetSelected;
  final Color? accentColor;

  const AppleStylePresetRow({
    super.key,
    required this.presets,
    this.selectedPreset,
    this.onPresetSelected,
    this.accentColor,
  });

  @override
  Widget build(BuildContext context) {
    final accentColor = this.accentColor ?? AppTheme.accentBlue;

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: presets.map((preset) {
          final isSelected = selectedPreset == preset.id;
          return Padding(
            padding: const EdgeInsets.only(right: AppTheme.spacing8),
            child: AppleStyleButton(
              onTap: () => onPresetSelected?.call(preset.id),
              backgroundColor: isSelected 
                  ? accentColor 
                  : AppTheme.systemGray.withOpacity(0.3),
              foregroundColor: isSelected 
                  ? AppTheme.primaryWhite 
                  : AppTheme.systemGray,
              isCircular: false,
              padding: const EdgeInsets.symmetric(
                horizontal: AppTheme.spacing12,
                vertical: AppTheme.spacing8,
              ),
              child: Text(
                preset.label,
                style: AppTheme.labelSmall.copyWith(
                  color: isSelected 
                      ? AppTheme.primaryWhite 
                      : AppTheme.systemGray,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}

/// Apple-style auto/manual toggle
class AppleStyleAutoToggle extends StatelessWidget {
  final bool isAuto;
  final ValueChanged<bool>? onChanged;
  final Color? accentColor;
  final String autoLabel;
  final String manualLabel;

  const AppleStyleAutoToggle({
    super.key,
    required this.isAuto,
    this.onChanged,
    this.accentColor,
    this.autoLabel = 'AUTO',
    this.manualLabel = 'MANUAL',
  });

  @override
  Widget build(BuildContext context) {
    final accentColor = this.accentColor ?? AppTheme.accentBlue;

    return Container(
      decoration: BoxDecoration(
        color: AppTheme.systemGray.withOpacity(0.2),
        borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildToggleButton(
            label: autoLabel,
            isSelected: isAuto,
            onTap: () => onChanged?.call(true),
            accentColor: accentColor,
          ),
          _buildToggleButton(
            label: manualLabel,
            isSelected: !isAuto,
            onTap: () => onChanged?.call(false),
            accentColor: accentColor,
          ),
        ],
      ),
    );
  }

  Widget _buildToggleButton({
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
    required Color accentColor,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: AppTheme.animationFast,
        padding: const EdgeInsets.symmetric(
          horizontal: AppTheme.spacing16,
          vertical: AppTheme.spacing8,
        ),
        decoration: BoxDecoration(
          color: isSelected ? accentColor : Colors.transparent,
          borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
        ),
        child: Text(
          label,
          style: AppTheme.labelSmall.copyWith(
            color: isSelected ? AppTheme.primaryWhite : AppTheme.systemGray,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }
}

/// Data class for preset buttons
class AppleStylePreset {
  final String id;
  final String label;
  final dynamic value;
  final IconData? icon;

  const AppleStylePreset({
    required this.id,
    required this.label,
    required this.value,
    this.icon,
  });
}

/// Apple-style value display
class AppleStyleValueDisplay extends StatelessWidget {
  final String label;
  final String value;
  final Color? accentColor;
  final IconData? icon;

  const AppleStyleValueDisplay({
    super.key,
    required this.label,
    required this.value,
    this.accentColor,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    final accentColor = this.accentColor ?? AppTheme.accentBlue;

    return Container(
      padding: const EdgeInsets.all(AppTheme.spacing12),
      decoration: BoxDecoration(
        color: AppTheme.systemGray.withOpacity(0.2),
        borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (icon != null) ...[
            Icon(
              icon,
              size: 16,
              color: accentColor,
            ),
            const SizedBox(width: AppTheme.spacing8),
          ],
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                label,
                style: AppTheme.labelSmall.copyWith(
                  color: AppTheme.systemGray,
                ),
              ),
              Text(
                value,
                style: AppTheme.labelMedium.copyWith(
                  color: accentColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
