import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:path_provider/path_provider.dart';

/// Demo showing the slider hiding approach for clean capture
class SliderHidingDemo extends StatefulWidget {
  const SliderHidingDemo({super.key});

  @override
  State<SliderHidingDemo> createState() => _SliderHidingDemoState();
}

class _SliderHidingDemoState extends State<SliderHidingDemo>
    with TickerProviderStateMixin {
  final GlobalKey _repaintBoundaryKey = GlobalKey();
  String? _capturedImagePath;
  bool _isCapturing = false;
  
  // Simulated slider visibility states
  final ValueNotifier<bool> _showShutterSpeedSlider = ValueNotifier(false);
  final ValueNotifier<bool> _showExposureSlider = ValueNotifier(false);
  final ValueNotifier<bool> _showBlurSlider = ValueNotifier(false);
  
  // Simulated effects
  double _shutterSpeed = 1.0;
  double _blurIntensity = 2.0;
  
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();
    
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_animationController);
  }

  @override
  void dispose() {
    _animationController.dispose();
    _showShutterSpeedSlider.dispose();
    _showExposureSlider.dispose();
    _showBlurSlider.dispose();
    super.dispose();
  }

  /// Temporarily hide all visible sliders for clean capture
  Map<String, bool> _hideAllSliders() {
    // Store current visibility states
    final currentStates = {
      'shutterSpeed': _showShutterSpeedSlider.value,
      'exposure': _showExposureSlider.value,
      'blur': _showBlurSlider.value,
    };
    
    // Hide all sliders
    _showShutterSpeedSlider.value = false;
    _showExposureSlider.value = false;
    _showBlurSlider.value = false;
    
    debugPrint('Temporarily hidden sliders: $currentStates');
    return currentStates;
  }
  
  /// Restore slider visibility states
  void _restoreSliders(Map<String, bool> previousStates) {
    _showShutterSpeedSlider.value = previousStates['shutterSpeed'] ?? false;
    _showExposureSlider.value = previousStates['exposure'] ?? false;
    _showBlurSlider.value = previousStates['blur'] ?? false;
    
    debugPrint('Restored sliders: $previousStates');
  }

  /// Capture the RepaintBoundary as an image
  Future<Uint8List?> captureWidget() async {
    try {
      await Future.delayed(const Duration(milliseconds: 100));
      
      RenderRepaintBoundary? boundary = _repaintBoundaryKey.currentContext
          ?.findRenderObject() as RenderRepaintBoundary?;
      
      if (boundary == null) {
        debugPrint('RepaintBoundary not found');
        return null;
      }

      ui.Image image = await boundary.toImage(pixelRatio: 2.0);
      ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      
      if (byteData == null) {
        debugPrint('Failed to convert image to bytes');
        return null;
      }

      return byteData.buffer.asUint8List();
    } catch (e) {
      debugPrint('Error capturing widget: $e');
      return null;
    }
  }

  /// Save the captured image bytes to a file
  Future<String?> saveImage(Uint8List bytes) async {
    try {
      final Directory extDir = await getTemporaryDirectory();
      final testDir = await Directory(
        '${extDir.path}/slider_hiding_demo',
      ).create(recursive: true);
      
      final String filePath = 
          '${testDir.path}/demo_${DateTime.now().millisecondsSinceEpoch}.png';
      
      final file = File(filePath);
      await file.writeAsBytes(bytes);
      
      debugPrint('Demo image saved: $filePath');
      return filePath;
    } catch (e) {
      debugPrint('Error saving demo image: $e');
      return null;
    }
  }

  /// Handle capture with slider hiding logic
  Future<void> handleCapture() async {
    if (_isCapturing) return;

    setState(() {
      _isCapturing = true;
    });

    try {
      // Check if effects are active
      final hasShutterEffects = _shutterSpeed > 0;
      final hasBlurEffects = _blurIntensity > 0;
      
      if (hasShutterEffects || hasBlurEffects) {
        debugPrint('Effects detected - using slider hiding capture');
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Hiding sliders and capturing...'),
              duration: Duration(milliseconds: 1000),
            ),
          );
        }
        
        // Step 1: Hide all sliders temporarily
        final previousSliderStates = _hideAllSliders();
        
        try {
          // Step 2: Wait for UI to update (sliders to hide)
          await Future.delayed(const Duration(milliseconds: 350));
          
          // Step 3: Capture the preview (now without visible sliders)
          final imageBytes = await captureWidget();
          if (imageBytes != null) {
            final savedPath = await saveImage(imageBytes);
            if (savedPath != null) {
              setState(() {
                _capturedImagePath = savedPath;
              });
              
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Photo captured with effects (sliders hidden)!'),
                    backgroundColor: Colors.green,
                    duration: Duration(milliseconds: 1500),
                  ),
                );
              }
              return;
            }
          }
          
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Capture failed'),
                backgroundColor: Colors.orange,
                duration: Duration(milliseconds: 1500),
              ),
            );
          }
        } finally {
          // Step 4: Always restore sliders after capture
          _restoreSliders(previousSliderStates);
        }
      } else {
        // Simulate normal capture
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Using normal capture (no effects)'),
              backgroundColor: Colors.blue,
              duration: Duration(milliseconds: 1500),
            ),
          );
        }
      }
    } finally {
      setState(() {
        _isCapturing = false;
      });
    }
  }

  Widget _buildSimulatedCameraPreview() {
    return RepaintBoundary(
      key: _repaintBoundaryKey,
      child: Column(
        children: [
          // Simulated top controls
          Container(
            height: 80,
            color: Colors.black.withOpacity(0.8),
            child: const Center(
              child: Text(
                'TOP CONTROLS',
                style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
              ),
            ),
          ),
          
          // Simulated camera preview area
          Expanded(
            child: Stack(
              children: [
                // Base preview
                Container(
                  width: double.infinity,
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Colors.deepPurple, Colors.blue, Colors.teal],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.camera_alt,
                        size: 80,
                        color: Colors.white,
                      ),
                      const SizedBox(height: 20),
                      const Text(
                        'CAMERA PREVIEW WITH EFFECTS',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 10),
                      Text(
                        'Shutter: ${_shutterSpeed}s, Blur: ${_blurIntensity.toStringAsFixed(1)}',
                        style: const TextStyle(color: Colors.white70),
                      ),
                    ],
                  ),
                ),
                
                // Simulated motion blur effect
                if (_shutterSpeed > 0)
                  AnimatedBuilder(
                    animation: _animation,
                    builder: (context, child) {
                      return Positioned(
                        left: 50 + (_animation.value * 200),
                        top: 100,
                        child: Container(
                          width: 20,
                          height: 20,
                          decoration: BoxDecoration(
                            color: Colors.yellow.withOpacity(0.8),
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: Colors.yellow.withOpacity(0.3),
                                blurRadius: _shutterSpeed * 5,
                                spreadRadius: _shutterSpeed * 2,
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                
                // Simulated blur overlay
                if (_blurIntensity > 0)
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(_blurIntensity * 0.05),
                      ),
                      child: BackdropFilter(
                        filter: ui.ImageFilter.blur(
                          sigmaX: _blurIntensity,
                          sigmaY: _blurIntensity,
                        ),
                        child: Container(
                          color: Colors.transparent,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
          
          // Simulated sliders (these will be hidden during capture)
          ValueListenableBuilder<bool>(
            valueListenable: _showShutterSpeedSlider,
            builder: (context, isVisible, child) {
              return AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
                height: isVisible ? 120 : 0,
                child: isVisible ? Container(
                  color: Colors.orange.withOpacity(0.8),
                  child: const Center(
                    child: Text(
                      'SHUTTER SPEED SLIDER (HIDDEN DURING CAPTURE)',
                      style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                    ),
                  ),
                ) : null,
              );
            },
          ),
          
          ValueListenableBuilder<bool>(
            valueListenable: _showExposureSlider,
            builder: (context, isVisible, child) {
              return AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
                height: isVisible ? 120 : 0,
                child: isVisible ? Container(
                  color: Colors.green.withOpacity(0.8),
                  child: const Center(
                    child: Text(
                      'EXPOSURE SLIDER (HIDDEN DURING CAPTURE)',
                      style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                    ),
                  ),
                ) : null,
              );
            },
          ),
          
          ValueListenableBuilder<bool>(
            valueListenable: _showBlurSlider,
            builder: (context, isVisible, child) {
              return AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
                height: isVisible ? 120 : 0,
                child: isVisible ? Container(
                  color: Colors.purple.withOpacity(0.8),
                  child: const Center(
                    child: Text(
                      'BLUR SLIDER (HIDDEN DURING CAPTURE)',
                      style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                    ),
                  ),
                ) : null,
              );
            },
          ),
          
          // Simulated bottom controls
          Container(
            height: 200,
            color: Colors.black.withOpacity(0.8),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text(
                  'BOTTOM CONTROLS',
                  style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 20),
                ElevatedButton(
                  onPressed: _isCapturing ? null : handleCapture,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.deepPurple,
                    padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 15),
                  ),
                  child: _isCapturing
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Text(
                          'Capture with Slider Hiding',
                          style: TextStyle(fontSize: 16, color: Colors.white),
                        ),
                ),
                if (_capturedImagePath != null)
                  Padding(
                    padding: const EdgeInsets.only(top: 10),
                    child: Text(
                      'Captured: ${_capturedImagePath!.split('/').last}',
                      style: TextStyle(fontSize: 12, color: Colors.green[300]),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Slider Hiding Demo'),
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          Expanded(
            child: _buildSimulatedCameraPreview(),
          ),
          
          // Controls
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.grey[900],
            child: Column(
              children: [
                // Slider toggle buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    ElevatedButton(
                      onPressed: () {
                        _showShutterSpeedSlider.value = !_showShutterSpeedSlider.value;
                      },
                      style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
                      child: const Text('SS Slider', style: TextStyle(color: Colors.white)),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        _showExposureSlider.value = !_showExposureSlider.value;
                      },
                      style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
                      child: const Text('Exp Slider', style: TextStyle(color: Colors.white)),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        _showBlurSlider.value = !_showBlurSlider.value;
                      },
                      style: ElevatedButton.styleFrom(backgroundColor: Colors.purple),
                      child: const Text('Blur Slider', style: TextStyle(color: Colors.white)),
                    ),
                  ],
                ),
                
                const SizedBox(height: 10),
                
                // Effect controls
                Row(
                  children: [
                    const Text('Shutter Speed:', style: TextStyle(color: Colors.white)),
                    Expanded(
                      child: Slider(
                        value: _shutterSpeed,
                        min: 0,
                        max: 2,
                        divisions: 20,
                        label: '${_shutterSpeed.toStringAsFixed(1)}s',
                        onChanged: (value) {
                          setState(() {
                            _shutterSpeed = value;
                          });
                        },
                      ),
                    ),
                  ],
                ),
                
                Row(
                  children: [
                    const Text('Blur:', style: TextStyle(color: Colors.white)),
                    Expanded(
                      child: Slider(
                        value: _blurIntensity,
                        min: 0,
                        max: 10,
                        divisions: 20,
                        label: _blurIntensity.toStringAsFixed(1),
                        onChanged: (value) {
                          setState(() {
                            _blurIntensity = value;
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
