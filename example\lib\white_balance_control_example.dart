import 'package:camerawesome/camerawesome_plugin.dart';
import 'package:flutter/material.dart';

void main() {
  runApp(const CameraAwesomeApp());
}

class CameraAwesomeApp extends StatelessWidget {
  const CameraAwesomeApp({super.key});

  @override
  Widget build(BuildContext context) {
    return const MaterialApp(
      title: 'White Balance Control Example',
      home: CameraPage(),
    );
  }
}

class CameraPage extends StatelessWidget {
  const CameraPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CameraAwesomeBuilder.awesome(
        saveConfig: SaveConfig.photo(),
        sensorConfig: SensorConfig.single(
          sensor: Sensor.position(SensorPosition.back),
          aspectRatio: CameraAspectRatios.ratio_4_3,
        ),
        topActionsBuilder: (state) => AwesomeTopActions(
          state: state,
          children: [
            AwesomeFlashButton(state: state),
            const Spacer(),
            AwesomeCameraSwitchButton(
              state: state,
              scale: 1.0,
              onSwitchTap: (state) {
                state.switchCameraSensor(
                  aspectRatio: state.sensorConfig.aspectRatio,
                );
              },
            ),
          ],
        ),
        middleContentBuilder: (state) {
          return Column(
            children: [
              const Spacer(),
              // White balance control positioned in the middle-right area
              Align(
                alignment: Alignment.centerRight,
                child: Padding(
                  padding: const EdgeInsets.only(right: 20),
                  child: AwesomeWhiteBalanceSelector(
                    state: state,
                    sliderActiveColor: Colors.white,
                    sliderInactiveColor: Colors.white.withOpacity(0.3),
                    textColor: Colors.white,
                  ),
                ),
              ),
              const Spacer(),
            ],
          );
        },
        bottomActionsBuilder: (state) => AwesomeBottomActions(
          state: state,
          left: AwesomeFlashButton(state: state),
          captureButton: AwesomeCaptureButton(
            state: state,
          ),
          right: AwesomeCameraSwitchButton(
            state: state,
            scale: 1.0,
            onSwitchTap: (state) {
              state.switchCameraSensor(
                aspectRatio: state.sensorConfig.aspectRatio,
              );
            },
          ),
        ),
      ),
    );
  }
}

/// Example with compact white balance control at the bottom
class CompactWhiteBalanceExample extends StatelessWidget {
  const CompactWhiteBalanceExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CameraAwesomeBuilder.awesome(
        saveConfig: SaveConfig.photo(),
        sensorConfig: SensorConfig.single(
          sensor: Sensor.position(SensorPosition.back),
          aspectRatio: CameraAspectRatios.ratio_1_1,
        ),
        bottomActionsBuilder: (state) => Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Compact white balance control at the bottom
            AwesomeCompactWhiteBalanceSelector(
              state: state,
              sliderActiveColor: Colors.white,
              sliderInactiveColor: Colors.white.withOpacity(0.3),
            ),
            const SizedBox(height: 8),
            AwesomeBottomActions(
              state: state,
              captureButton: AwesomeCaptureButton(state: state),
            ),
          ],
        ),
      ),
    );
  }
}

/// Example with custom styling and multiple controls
class ProfessionalWhiteBalanceExample extends StatelessWidget {
  const ProfessionalWhiteBalanceExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CameraAwesomeBuilder.awesome(
        saveConfig: SaveConfig.photo(),
        sensorConfig: SensorConfig.single(
          sensor: Sensor.position(SensorPosition.back),
          aspectRatio: CameraAspectRatios.ratio_4_3,
        ),
        middleContentBuilder: (state) {
          return Column(
            children: [
              const Spacer(),
              Align(
                alignment: Alignment.centerRight,
                child: Padding(
                  padding: const EdgeInsets.only(right: 20),
                  child: Column(
                    children: [
                      // Exposure control
                      AwesomeExposureSelector(
                        state: state,
                        sliderActiveColor: Colors.amber,
                        sliderInactiveColor: Colors.amber.withOpacity(0.3),
                        textColor: Colors.white,
                        showButton: true,
                      ),
                      const SizedBox(height: 16),
                      // White balance control with custom colors
                      AwesomeWhiteBalanceSelector(
                        state: state,
                        sliderActiveColor: Colors.cyan,
                        sliderInactiveColor: Colors.cyan.withOpacity(0.3),
                        textColor: Colors.white,
                      ),
                    ],
                  ),
                ),
              ),
              const Spacer(),
            ],
          );
        },
      ),
    );
  }
}

/// Example demonstrating programmatic white balance control
class ProgrammaticWhiteBalanceExample extends StatefulWidget {
  const ProgrammaticWhiteBalanceExample({super.key});

  @override
  State<ProgrammaticWhiteBalanceExample> createState() => _ProgrammaticWhiteBalanceExampleState();
}

class _ProgrammaticWhiteBalanceExampleState extends State<ProgrammaticWhiteBalanceExample> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CameraAwesomeBuilder.awesome(
        saveConfig: SaveConfig.photo(),
        sensorConfig: SensorConfig.single(
          sensor: Sensor.position(SensorPosition.back),
          aspectRatio: CameraAspectRatios.ratio_4_3,
        ),
        topActionsBuilder: (state) => AwesomeTopActions(
          state: state,
          children: [
            // Programmatic white balance test buttons
            IconButton(
              onPressed: () => _testWhiteBalance(state),
              icon: const Icon(Icons.wb_auto, color: Colors.white),
              tooltip: 'Test WB Modes',
            ),
            const Spacer(),
            AwesomeCameraSwitchButton(state: state),
          ],
        ),
        middleContentBuilder: (state) {
          return Column(
            children: [
              const Spacer(),
              // White balance control
              Align(
                alignment: Alignment.centerRight,
                child: Padding(
                  padding: const EdgeInsets.only(right: 20),
                  child: AwesomeWhiteBalanceSelector(
                    state: state,
                    sliderActiveColor: Colors.white,
                    sliderInactiveColor: Colors.white.withOpacity(0.3),
                    textColor: Colors.white,
                  ),
                ),
              ),
              const Spacer(),
              // Status display
              Container(
                margin: const EdgeInsets.all(20),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.black54,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: StreamBuilder<double>(
                  stream: state.whiteBalance$,
                  builder: (context, wbSnapshot) {
                    return StreamBuilder<bool>(
                      stream: state.whiteBalanceAuto$,
                      builder: (context, autoSnapshot) {
                        final kelvin = wbSnapshot.data ?? -1.0;
                        final isAuto = autoSnapshot.data ?? true;

                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Text(
                              'White Balance Status:',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Mode: ${isAuto ? 'Auto' : 'Manual'}',
                              style: const TextStyle(color: Colors.white70),
                            ),
                            Text(
                              'Temperature: ${kelvin == -1.0 ? 'Auto' : '${kelvin.round()}K'}',
                              style: const TextStyle(color: Colors.white70),
                            ),
                          ],
                        );
                      },
                    );
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  void _testWhiteBalance(CameraState state) async {
    // Test sequence of white balance modes
    final testSequence = [
      (-1.0, 'Auto'),
      (3000.0, 'Tungsten'),
      (4200.0, 'Fluorescent'),
      (5500.0, 'Daylight'),
      (6500.0, 'Cloudy'),
      (7000.0, 'Sunny'),
    ];

    for (final (kelvin, name) in testSequence) {
      print('Testing white balance: $name (${kelvin}K)');
      await state.setWhiteBalance(kelvin);
      await Future.delayed(const Duration(seconds: 2));
    }

    // Return to auto mode
    await state.setWhiteBalanceAuto(true);
    print('White balance test completed - returned to auto mode');
  }
}
