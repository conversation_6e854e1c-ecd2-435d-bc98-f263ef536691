import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:path_provider/path_provider.dart';

/// Test widget to verify RepaintBoundary capture functionality
class TestRepaintBoundaryCapture extends StatefulWidget {
  const TestRepaintBoundaryCapture({super.key});

  @override
  State<TestRepaintBoundaryCapture> createState() => _TestRepaintBoundaryCaptureState();
}

class _TestRepaintBoundaryCaptureState extends State<TestRepaintBoundaryCapture> {
  final GlobalKey _repaintBoundaryKey = GlobalKey();
  String? _savedImagePath;
  bool _isCapturing = false;

  /// Capture the RepaintBoundary as an image
  Future<Uint8List?> captureWidget() async {
    try {
      RenderRepaintBoundary? boundary = _repaintBoundaryKey.currentContext
          ?.findRenderObject() as RenderRepaintBoundary?;
      
      if (boundary == null) {
        debugPrint('RepaintBoundary not found');
        return null;
      }

      // Capture the boundary as an image with high resolution
      ui.Image image = await boundary.toImage(pixelRatio: 2.0);
      ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      
      if (byteData == null) {
        debugPrint('Failed to convert image to bytes');
        return null;
      }

      return byteData.buffer.asUint8List();
    } catch (e) {
      debugPrint('Error capturing widget: $e');
      return null;
    }
  }

  /// Save the captured image bytes to a file
  Future<String?> saveImage(Uint8List bytes) async {
    try {
      final Directory extDir = await getTemporaryDirectory();
      final testDir = await Directory(
        '${extDir.path}/test_captures',
      ).create(recursive: true);
      
      final String filePath = 
          '${testDir.path}/test_capture_${DateTime.now().millisecondsSinceEpoch}.png';
      
      final file = File(filePath);
      await file.writeAsBytes(bytes);
      
      debugPrint('Test image saved: $filePath');
      return filePath;
    } catch (e) {
      debugPrint('Error saving test image: $e');
      return null;
    }
  }

  /// Handle capture button tap
  Future<void> handleCapture() async {
    if (_isCapturing) return;

    setState(() {
      _isCapturing = true;
    });

    try {
      final imageBytes = await captureWidget();
      if (imageBytes != null) {
        final savedPath = await saveImage(imageBytes);
        if (savedPath != null) {
          setState(() {
            _savedImagePath = savedPath;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Image captured and saved: $savedPath')),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Failed to save image')),
          );
        }
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to capture image')),
        );
      }
    } finally {
      setState(() {
        _isCapturing = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('RepaintBoundary Test'),
        backgroundColor: Colors.blue,
      ),
      body: Column(
        children: [
          Expanded(
            child: RepaintBoundary(
              key: _repaintBoundaryKey,
              child: Container(
                width: double.infinity,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.purple, Colors.blue, Colors.green],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.camera_alt,
                      size: 100,
                      color: Colors.white,
                    ),
                    const SizedBox(height: 20),
                    const Text(
                      'Test RepaintBoundary Capture',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 10),
                    Text(
                      'Time: ${DateTime.now().toString()}',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Colors.white70,
                      ),
                    ),
                    const SizedBox(height: 20),
                    // Simulate some effects
                    Container(
                      width: 200,
                      height: 50,
                      decoration: BoxDecoration(
                        color: Colors.orange.withOpacity(0.8),
                        borderRadius: BorderRadius.circular(25),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.3),
                            blurRadius: 10,
                            offset: const Offset(0, 5),
                          ),
                        ],
                      ),
                      child: const Center(
                        child: Text(
                          'Simulated Effect',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                ElevatedButton(
                  onPressed: _isCapturing ? null : handleCapture,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 15),
                  ),
                  child: _isCapturing
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Text(
                          'Capture RepaintBoundary',
                          style: TextStyle(fontSize: 16, color: Colors.white),
                        ),
                ),
                const SizedBox(height: 10),
                if (_savedImagePath != null)
                  Text(
                    'Last capture: ${_savedImagePath!.split('/').last}',
                    style: const TextStyle(fontSize: 12, color: Colors.grey),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
