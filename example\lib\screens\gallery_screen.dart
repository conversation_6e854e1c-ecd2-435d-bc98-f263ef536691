import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:video_player/video_player.dart';
import 'package:photo_view/photo_view.dart';
import 'package:gal/gal.dart';
import 'package:permission_handler/permission_handler.dart';
import '../theme/app_theme.dart';
import '../widgets/apple_style_button.dart';
import 'image_viewer_screen.dart';
import 'video_player_screen.dart';

class GalleryScreen extends StatefulWidget {
  const GalleryScreen({super.key});

  @override
  State<GalleryScreen> createState() => _GalleryScreenState();
}

class _GalleryScreenState extends State<GalleryScreen> {
  List<FileSystemEntity> _mediaFiles = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadMediaFiles();
  }

  Future<void> _loadMediaFiles() async {
    try {
      final Directory extDir = await getTemporaryDirectory();
      final camerawesomeDir = Directory('${extDir.path}/camerawesome');

      if (await camerawesomeDir.exists()) {
        final files = camerawesomeDir.listSync()
          ..sort((a, b) => b.statSync().modified.compareTo(a.statSync().modified));

        setState(() {
          _mediaFiles = files.where((file) {
            final extension = file.path.toLowerCase().split('.').last;
            return ['jpg', 'jpeg', 'png', 'mp4', 'mov'].contains(extension);
          }).toList();
          _isLoading = false;
        });
      } else {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading media files: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  bool _isVideo(String path) {
    final extension = path.toLowerCase().split('.').last;
    return ['mp4', 'mov'].contains(extension);
  }

  Future<void> _saveToGallery(String filePath) async {
    try {
      // Request permissions
      if (!await Gal.hasAccess()) {
        final hasAccess = await Gal.requestAccess();
        if (!hasAccess) {
          _showSnackBar('Gallery access denied');
          return;
        }
      }

      await Gal.putImage(filePath);
      _showSnackBar('Saved to gallery successfully');
    } catch (e) {
      debugPrint('Error saving to gallery: $e');
      _showSnackBar('Error saving to gallery');
    }
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.black87,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _openFullscreen(String filePath) {
    AppTheme.lightHaptic();
    if (_isVideo(filePath)) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => VideoPlayerScreen(filePath: filePath),
        ),
      );
    } else {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ImageViewerScreen(filePath: filePath),
        ),
      );
    }
  }

  void _showSortOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: AppTheme.darkGlassBackgroundStrong,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(AppTheme.radiusLarge),
            topRight: Radius.circular(AppTheme.radiusLarge),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: AppTheme.spacing12),
              decoration: BoxDecoration(
                color: AppTheme.systemGray3,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            Padding(
              padding: const EdgeInsets.all(AppTheme.spacing20),
              child: Column(
                children: [
                  const Text(
                    'Sort Options',
                    style: AppTheme.headlineMedium,
                  ),
                  const SizedBox(height: AppTheme.spacing20),

                  _buildSortOption('Date (Newest First)', Icons.access_time),
                  _buildSortOption('Date (Oldest First)', Icons.history),
                  _buildSortOption('Name (A-Z)', Icons.sort_by_alpha),
                  _buildSortOption('Size (Largest First)', Icons.storage),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSortOption(String title, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppTheme.spacing12),
      child: AppleStyleButton(
        onTap: () {
          Navigator.pop(context);
          // TODO: Implement sorting logic
        },
        backgroundColor: AppTheme.systemGray.withOpacity(0.3),
        isCircular: false,
        padding: const EdgeInsets.all(AppTheme.spacing16),
        child: Row(
          children: [
            Icon(icon, color: AppTheme.primaryWhite),
            const SizedBox(width: AppTheme.spacing12),
            Text(
              title,
              style: AppTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMediaThumbnail(FileSystemEntity file, int index) {
    return GestureDetector(
      onTap: () => _openFullscreen(file.path),
      onLongPress: () => _showMediaOptions(context, file.path),
      child: Hero(
        tag: 'media_${file.path}',
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
            boxShadow: AppTheme.shadowSmall,
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
            child: Stack(
              fit: StackFit.expand,
              children: [
                _isVideo(file.path)
                    ? VideoThumbnail(filePath: file.path)
                    : Image.file(
                        File(file.path),
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color: AppTheme.systemGray4,
                            child: const Icon(
                              Icons.broken_image,
                              color: AppTheme.systemGray,
                              size: 32,
                            ),
                          );
                        },
                      ),

                // Video indicator
                if (_isVideo(file.path))
                  Positioned(
                    top: AppTheme.spacing8,
                    right: AppTheme.spacing8,
                    child: Container(
                      padding: const EdgeInsets.all(AppTheme.spacing4),
                      decoration: BoxDecoration(
                        color: AppTheme.primaryBlack.withOpacity(0.7),
                        borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
                      ),
                      child: const Icon(
                        Icons.play_arrow,
                        color: AppTheme.primaryWhite,
                        size: 16,
                      ),
                    ),
                  ),

                // Selection overlay (for future multi-select feature)
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
                      border: Border.all(
                        color: AppTheme.primaryWhite.withOpacity(0.1),
                        width: 0.5,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showMediaOptions(BuildContext context, String filePath) {
    AppTheme.mediumHaptic();
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: AppTheme.darkGlassBackgroundStrong,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(AppTheme.radiusLarge),
            topRight: Radius.circular(AppTheme.radiusLarge),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: AppTheme.spacing12),
              decoration: BoxDecoration(
                color: AppTheme.systemGray3,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            Padding(
              padding: const EdgeInsets.all(AppTheme.spacing20),
              child: Column(
                children: [
                  _buildMediaOption(
                    'Save to Gallery',
                    Icons.download,
                    () {
                      Navigator.pop(context);
                      _saveToGallery(filePath);
                    },
                  ),
                  _buildMediaOption(
                    'Share',
                    Icons.share,
                    () {
                      Navigator.pop(context);
                      // TODO: Implement share functionality
                    },
                  ),
                  _buildMediaOption(
                    'Delete',
                    Icons.delete,
                    () {
                      Navigator.pop(context);
                      _showDeleteConfirmation(context, filePath);
                    },
                    isDestructive: true,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMediaOption(String title, IconData icon, VoidCallback onTap, {bool isDestructive = false}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppTheme.spacing12),
      child: AppleStyleButton(
        onTap: onTap,
        backgroundColor: isDestructive
            ? AppTheme.accentRed.withOpacity(0.2)
            : AppTheme.systemGray.withOpacity(0.3),
        isCircular: false,
        padding: const EdgeInsets.all(AppTheme.spacing16),
        child: Row(
          children: [
            Icon(
              icon,
              color: isDestructive ? AppTheme.accentRed : AppTheme.primaryWhite,
            ),
            const SizedBox(width: AppTheme.spacing12),
            Text(
              title,
              style: AppTheme.bodyMedium.copyWith(
                color: isDestructive ? AppTheme.accentRed : AppTheme.primaryWhite,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context, String filePath) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.backgroundSecondary,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
        ),
        title: const Text(
          'Delete Media',
          style: AppTheme.headlineSmall,
        ),
        content: Text(
          'Are you sure you want to delete this ${_isVideo(filePath) ? 'video' : 'photo'}? This action cannot be undone.',
          style: AppTheme.bodyMedium.copyWith(
            color: AppTheme.systemGray,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: AppTheme.labelLarge.copyWith(
                color: AppTheme.accentBlue,
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteFile(filePath);
            },
            child: Text(
              'Delete',
              style: AppTheme.labelLarge.copyWith(
                color: AppTheme.accentRed,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _deleteFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        setState(() {
          _mediaFiles.removeWhere((f) => f.path == filePath);
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                '${_isVideo(filePath) ? 'Video' : 'Photo'} deleted successfully',
                style: AppTheme.bodyMedium,
              ),
              backgroundColor: AppTheme.backgroundSecondary,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
              ),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to delete file: $e',
              style: AppTheme.bodyMedium,
            ),
            backgroundColor: AppTheme.accentRed,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
            ),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundPrimary,
      body: CustomScrollView(
        slivers: [
          // Apple-style app bar
          SliverAppBar(
            backgroundColor: AppTheme.backgroundPrimary,
            elevation: 0,
            pinned: true,
            expandedHeight: 100,
            leading: AppleStyleButton(
              onTap: () {
                AppTheme.lightHaptic();
                Navigator.pop(context);
              },
              size: AppTheme.preferredTouchTarget,
              child: const Icon(Icons.arrow_back_ios),
            ),
            flexibleSpace: FlexibleSpaceBar(
              title: Text(
                'Gallery',
                style: AppTheme.headlineLarge.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              ),
              titlePadding: const EdgeInsets.only(
                left: AppTheme.spacing64,
                bottom: AppTheme.spacing16,
              ),
            ),
            actions: [
              AppleStyleButton(
                onTap: () => _showSortOptions(context),
                tooltip: 'Sort Options',
                child: const Icon(Icons.sort),
              ),
              const SizedBox(width: AppTheme.spacing8),
            ],
          ),

          // Content
          _isLoading
              ? SliverFillRemaining(
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const CircularProgressIndicator(
                          color: AppTheme.accentBlue,
                          strokeWidth: 3,
                        ),
                        const SizedBox(height: AppTheme.spacing16),
                        Text(
                          'Loading media...',
                          style: AppTheme.bodyMedium.copyWith(
                            color: AppTheme.systemGray,
                          ),
                        ),
                      ],
                    ),
                  ),
                )
              : _mediaFiles.isEmpty
                  ? SliverFillRemaining(
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(
                              Icons.photo_library_outlined,
                              size: 64,
                              color: AppTheme.systemGray3,
                            ),
                            const SizedBox(height: AppTheme.spacing16),
                            Text(
                              'No Photos or Videos',
                              style: AppTheme.headlineSmall.copyWith(
                                color: AppTheme.systemGray,
                              ),
                            ),
                            const SizedBox(height: AppTheme.spacing8),
                            Text(
                              'Take some photos or videos to see them here',
                              style: AppTheme.bodyMedium.copyWith(
                                color: AppTheme.systemGray2,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    )
                  : SliverPadding(
                      padding: const EdgeInsets.all(AppTheme.spacing16),
                      sliver: SliverGrid(
                        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 3,
                          crossAxisSpacing: AppTheme.spacing8,
                          mainAxisSpacing: AppTheme.spacing8,
                          childAspectRatio: 1.0,
                        ),
                        delegate: SliverChildBuilderDelegate(
                          (context, index) {
                            final file = _mediaFiles[index];
                            return _buildMediaThumbnail(file, index);
                          },
                          childCount: _mediaFiles.length,
                        ),
                      ),
                    ),
        ],
      ),
    );
  }
}

class VideoThumbnail extends StatefulWidget {
  final String filePath;

  const VideoThumbnail({super.key, required this.filePath});

  @override
  State<VideoThumbnail> createState() => _VideoThumbnailState();
}

class _VideoThumbnailState extends State<VideoThumbnail> {
  VideoPlayerController? _controller;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeVideo();
  }

  Future<void> _initializeVideo() async {
    _controller = VideoPlayerController.file(File(widget.filePath));
    try {
      await _controller!.initialize();
      setState(() {
        _isInitialized = true;
      });
    } catch (e) {
      debugPrint('Error initializing video: $e');
    }
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized || _controller == null) {
      return Container(
        decoration: BoxDecoration(
          color: AppTheme.systemGray4,
          borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.video_file,
                color: AppTheme.systemGray,
                size: 32,
              ),
              const SizedBox(height: AppTheme.spacing4),
              Text(
                'Loading...',
                style: AppTheme.labelSmall.copyWith(
                  color: AppTheme.systemGray,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Stack(
      fit: StackFit.expand,
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
          child: AspectRatio(
            aspectRatio: _controller!.value.aspectRatio,
            child: VideoPlayer(_controller!),
          ),
        ),

        // Play button overlay with glass morphism
        Center(
          child: Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: AppTheme.primaryBlack.withOpacity(0.6),
              shape: BoxShape.circle,
              border: Border.all(
                color: AppTheme.primaryWhite.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: const Icon(
              Icons.play_arrow,
              color: AppTheme.primaryWhite,
              size: 24,
            ),
          ),
        ),

        // Duration badge (if available)
        if (_controller!.value.duration.inSeconds > 0)
          Positioned(
            bottom: AppTheme.spacing8,
            right: AppTheme.spacing8,
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppTheme.spacing8,
                vertical: AppTheme.spacing4,
              ),
              decoration: BoxDecoration(
                color: AppTheme.primaryBlack.withOpacity(0.7),
                borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
              ),
              child: Text(
                _formatDuration(_controller!.value.duration),
                style: AppTheme.labelSmall.copyWith(
                  color: AppTheme.primaryWhite,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
      ],
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));

    if (duration.inHours > 0) {
      final hours = twoDigits(duration.inHours);
      return '$hours:$minutes:$seconds';
    }
    return '$minutes:$seconds';
  }
}
