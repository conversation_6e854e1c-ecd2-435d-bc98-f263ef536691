import 'dart:async'; // Import for StreamSubscription
import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;

// import 'package:better_open_file/better_open_file.dart';
import 'package:camerawesome/camerawesome_plugin.dart';
import 'package:camerawesome/pigeon.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:path_provider/path_provider.dart'; // Import for getTemporaryDirectory
import 'utils/file_utils.dart';
import 'screens/gallery_screen.dart';
import 'screens/image_viewer_screen.dart';
import 'screens/video_player_screen.dart';
import 'package:camerawesome/src/widgets/buttons/awesome_flash_button.dart' as awesome_flash_button; // Added alias
import 'package:camerawesome/src/widgets/buttons/awesome_aspect_ratio_button.dart';
import 'package:camerawesome/src/widgets/buttons/awesome_location_button.dart';

import 'package:camerawesome/src/widgets/buttons/awesome_camera_switch_button.dart';

void main() {
  runApp(const CameraAwesomeApp());
}

class CameraAwesomeApp extends StatelessWidget {
  const CameraAwesomeApp({super.key});

  @override
  Widget build(BuildContext context) {
    return const MaterialApp(
      title: 'camerAwesome',
      home: CameraPage(),
    );
  }
}

class CameraPage extends StatefulWidget {
  const CameraPage({super.key});

  @override
  State<CameraPage> createState() => _CameraPageState();
}

class _CameraPageState extends State<CameraPage> {
  // ValueNotifiers to control the visibility of the sliders
  final ValueNotifier<bool> _showBlurSlider = ValueNotifier(false);
  final ValueNotifier<bool> _showShutterSpeedSlider = ValueNotifier(false);
  final ValueNotifier<bool> _showExposureSlider = ValueNotifier(false);
  final ValueNotifier<bool> _showWhiteBalanceSlider = ValueNotifier(false);
  final ValueNotifier<bool> _showISOSlider = ValueNotifier(false);

  // Add a StreamSubscription to listen to blur changes
  StreamSubscription<double>? _blurSubscription;

  // ValueNotifier for last captured image path
  final ValueNotifier<String?> _lastCapturedFilePathNotifier = ValueNotifier(null);

  // GlobalKey for RepaintBoundary to capture preview with effects
  final GlobalKey _previewContainerKey = GlobalKey();

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _blurSubscription?.cancel(); // Cancel subscription on dispose
    _showBlurSlider.dispose();
    _showShutterSpeedSlider.dispose();
    _showExposureSlider.dispose();
    _showWhiteBalanceSlider.dispose();
    _showISOSlider.dispose();
    _lastCapturedFilePathNotifier.dispose(); // Dispose the notifier
    super.dispose();
  }

  /// Calculate the preview area bounds within the captured image
  /// Since sliders are now hidden during capture, we only need to exclude fixed UI elements
  Rect? _calculatePreviewBounds(Size capturedImageSize) {
    try {
      // Get the screen size
      final screenSize = MediaQuery.of(context).size;
      final screenPadding = MediaQuery.of(context).padding;

      // Calculate fixed UI elements (sliders are hidden during capture)
      // Top area: status bar + top actions bar + zoom selector
      final topControlsHeight = screenPadding.top + 80 + 40; // Status bar + top actions + zoom

      // Bottom area: main controls section (camera mode selector + capture buttons)
      final bottomControlsHeight = 200; // Main controls section height

      // Add small padding for safety
      const extraPadding = 10;
      final totalTopHeight = topControlsHeight + extraPadding;
      final totalBottomHeight = bottomControlsHeight + extraPadding;

      // Available preview area (full height since sliders are hidden)
      final availableHeight = screenSize.height - totalTopHeight - totalBottomHeight;
      final availableWidth = screenSize.width;

      // Ensure we have a valid preview area
      if (availableHeight <= 0 || availableWidth <= 0) {
        debugPrint('Invalid preview area: width=$availableWidth, height=$availableHeight');
        return null;
      }

      // Calculate preview bounds (centered in available area)
      final previewTop = totalTopHeight;
      final previewLeft = 0.0;
      final previewWidth = availableWidth;
      final previewHeight = availableHeight;

      // Convert to captured image coordinates (accounting for pixel ratio)
      const pixelRatio = 2.0;
      final scaledTop = previewTop * pixelRatio;
      final scaledLeft = previewLeft * pixelRatio;
      final scaledWidth = previewWidth * pixelRatio;
      final scaledHeight = previewHeight * pixelRatio;

      debugPrint('Simplified Preview Bounds (sliders hidden):');
      debugPrint('  - Screen: ${screenSize.width}x${screenSize.height}');
      debugPrint('  - Top controls: $totalTopHeight');
      debugPrint('  - Bottom controls: $totalBottomHeight');
      debugPrint('  - Available preview: ${previewWidth}x$previewHeight');
      debugPrint('  - Scaled bounds: top=$scaledTop, left=$scaledLeft, width=$scaledWidth, height=$scaledHeight');

      return Rect.fromLTWH(scaledLeft, scaledTop, scaledWidth, scaledHeight);
    } catch (e) {
      debugPrint('Error calculating preview bounds: $e');
      return null;
    }
  }

  /// Crop the captured image to show only the preview area
  Future<Uint8List?> _cropImageToPreview(Uint8List imageBytes) async {
    try {
      // Decode the image
      final ui.Codec codec = await ui.instantiateImageCodec(imageBytes);
      final ui.FrameInfo frameInfo = await codec.getNextFrame();
      final ui.Image originalImage = frameInfo.image;

      final imageSize = Size(originalImage.width.toDouble(), originalImage.height.toDouble());
      final previewBounds = _calculatePreviewBounds(imageSize);

      if (previewBounds == null) {
        debugPrint('Could not calculate preview bounds, returning original image');
        return imageBytes;
      }

      // Create a new canvas for the cropped image
      final recorder = ui.PictureRecorder();
      final canvas = Canvas(recorder);

      // Draw only the preview area
      canvas.drawImageRect(
        originalImage,
        previewBounds,
        Rect.fromLTWH(0, 0, previewBounds.width, previewBounds.height),
        Paint(),
      );

      // Convert to image
      final picture = recorder.endRecording();
      final croppedImage = await picture.toImage(
        previewBounds.width.toInt(),
        previewBounds.height.toInt(),
      );

      // Convert to bytes
      final ByteData? byteData = await croppedImage.toByteData(format: ui.ImageByteFormat.png);

      if (byteData == null) {
        debugPrint('Failed to convert cropped image to bytes');
        return imageBytes; // Return original if cropping fails
      }

      debugPrint('Successfully cropped image to preview area: ${byteData.lengthInBytes} bytes');
      return byteData.buffer.asUint8List();
    } catch (e) {
      debugPrint('Error cropping image: $e');
      return imageBytes; // Return original if cropping fails
    }
  }

  /// Capture the preview with all effects using RepaintBoundary
  Future<Uint8List?> captureFilteredPreview() async {
    try {
      // Wait a frame to ensure the RepaintBoundary is properly rendered
      await Future.delayed(const Duration(milliseconds: 100));

      RenderRepaintBoundary? boundary = _previewContainerKey.currentContext
          ?.findRenderObject() as RenderRepaintBoundary?;

      if (boundary == null) {
        debugPrint('RepaintBoundary not found - context may not be available');
        return null;
      }

      // Check if the boundary needs to be painted
      if (!boundary.debugNeedsPaint) {
        // Force a repaint if needed
        boundary.markNeedsPaint();
        await Future.delayed(const Duration(milliseconds: 50));
      }

      // Capture the boundary as an image with high resolution
      ui.Image image = await boundary.toImage(pixelRatio: 2.0);
      ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);

      if (byteData == null) {
        debugPrint('Failed to convert image to bytes');
        return null;
      }

      debugPrint('Successfully captured RepaintBoundary: ${byteData.lengthInBytes} bytes');

      // Crop the image to show only the preview area
      final croppedImageBytes = await _cropImageToPreview(byteData.buffer.asUint8List());

      return croppedImageBytes;
    } catch (e) {
      debugPrint('Error capturing filtered preview: $e');
      return null;
    }
  }

  /// Save the captured image bytes to a file
  Future<String?> saveFilteredImage(Uint8List bytes) async {
    try {
      final Directory extDir = await getTemporaryDirectory();
      final testDir = await Directory(
        '${extDir.path}/camerawesome',
      ).create(recursive: true);

      final String filePath =
          '${testDir.path}/filtered_${DateTime.now().millisecondsSinceEpoch}.png';

      final file = File(filePath);
      await file.writeAsBytes(bytes);

      debugPrint('Filtered image saved: $filePath');
      return filePath;
    } catch (e) {
      debugPrint('Error saving filtered image: $e');
      return null;
    }
  }

  /// Temporarily hide all visible sliders for clean capture
  Map<String, bool> _hideAllSliders() {
    // Store current visibility states
    final currentStates = {
      'shutterSpeed': _showShutterSpeedSlider.value,
      'exposure': _showExposureSlider.value,
      'blur': _showBlurSlider.value,
      'iso': _showISOSlider.value,
      'whiteBalance': _showWhiteBalanceSlider.value,
    };

    // Hide all sliders
    _showShutterSpeedSlider.value = false;
    _showExposureSlider.value = false;
    _showBlurSlider.value = false;
    _showISOSlider.value = false;
    _showWhiteBalanceSlider.value = false;

    debugPrint('Temporarily hidden sliders: $currentStates');
    return currentStates;
  }

  /// Restore slider visibility states
  void _restoreSliders(Map<String, bool> previousStates) {
    _showShutterSpeedSlider.value = previousStates['shutterSpeed'] ?? false;
    _showExposureSlider.value = previousStates['exposure'] ?? false;
    _showBlurSlider.value = previousStates['blur'] ?? false;
    _showISOSlider.value = previousStates['iso'] ?? false;
    _showWhiteBalanceSlider.value = previousStates['whiteBalance'] ?? false;

    debugPrint('Restored sliders: $previousStates');
  }

  /// Handle custom capture with effects
  Future<void> handleCustomCapture(CameraState state) async {
    if (state is! PhotoCameraState) return;

    // Check if shutter speed effects are active
    final shutterSpeed = state.shutterSpeed;
    final hasShutterEffects = shutterSpeed > 0; // Manual shutter speed mode

    // Also check for other effects that might be active
    final blurIntensity = state.blurIntensity;
    final hasBlurEffects = blurIntensity > 0;

    if (hasShutterEffects || hasBlurEffects) {
      debugPrint('Effects detected - using RepaintBoundary capture');
      debugPrint('Shutter speed: $shutterSpeed, Blur: $blurIntensity');

      // Show capture feedback (check if mounted to avoid BuildContext issues)
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Capturing with effects...'),
            duration: Duration(milliseconds: 1000),
          ),
        );
      }

      // Step 1: Hide all sliders temporarily
      final previousSliderStates = _hideAllSliders();

      try {
        // Step 2: Wait for UI to update (sliders to hide)
        await Future.delayed(const Duration(milliseconds: 350)); // Slightly longer than animation duration

        // Step 3: Capture the preview with effects (now without visible sliders)
        final imageBytes = await captureFilteredPreview();
        if (imageBytes != null) {
          final savedPath = await saveFilteredImage(imageBytes);
          if (savedPath != null) {
            _lastCapturedFilePathNotifier.value = savedPath;
            debugPrint('Custom capture with effects completed: $savedPath');

            // Show success feedback (check if mounted to avoid BuildContext issues)
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Photo captured with effects!'),
                  backgroundColor: Colors.green,
                  duration: Duration(milliseconds: 1500),
                ),
              );
            }
            return;
          }
        }
        debugPrint('Failed to capture with effects, falling back to normal capture');

        // Show fallback feedback (check if mounted to avoid BuildContext issues)
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Effects capture failed, using normal capture'),
              backgroundColor: Colors.orange,
              duration: Duration(milliseconds: 1500),
            ),
          );
        }
      } finally {
        // Step 4: Always restore sliders after capture (success or failure)
        _restoreSliders(previousSliderStates);
      }
    }

    // Fallback to normal capture
    debugPrint('Using normal camera capture');
    state.takePhoto();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        color: Colors.white,
        child: RepaintBoundary(
          key: _previewContainerKey,
          child: CameraAwesomeBuilder.awesome(
          onMediaCaptureEvent: (event) {
            switch ((event.status, event.isPicture, event.isVideo)) {
              case (MediaCaptureStatus.capturing, true, false):
                debugPrint('Capturing picture...');
              case (MediaCaptureStatus.success, true, false):
                event.captureRequest.when(
                  single: (single) {
                    debugPrint('Picture saved: ${single.file?.path}');
                    _lastCapturedFilePathNotifier.value = single.file?.path; // Update notifier
                  },
                  multiple: (multiple) {
                    multiple.fileBySensor.forEach((key, value) {
                      debugPrint('multiple image taken: $key ${value?.path}');
                      if (_lastCapturedFilePathNotifier.value == null) { // Store the first one if multiple
                        _lastCapturedFilePathNotifier.value = value?.path;
                      }
                    });
                  },
                );
              case (MediaCaptureStatus.failure, true, false):
                debugPrint('Failed to capture picture: ${event.exception}');
              case (MediaCaptureStatus.capturing, false, true):
                debugPrint('Capturing video...');
              case (MediaCaptureStatus.success, false, true):
                event.captureRequest.when(
                  single: (single) {
                    debugPrint('Video saved: ${single.file?.path}');
                    _lastCapturedFilePathNotifier.value = single.file?.path; // Update notifier
                  },
                  multiple: (multiple) {
                    multiple.fileBySensor.forEach((key, value) {
                      debugPrint('multiple video taken: $key ${value?.path}');
                      if (_lastCapturedFilePathNotifier.value == null) { // Store the first one if multiple
                        _lastCapturedFilePathNotifier.value = value?.path;
                      }
                    });
                  },
                );
              case (MediaCaptureStatus.failure, false, true):
                debugPrint('Failed to capture video: ${event.exception}');
              default:
                debugPrint('Unknown event: $event');
            }
          },
          saveConfig: SaveConfig.photoAndVideo(
            initialCaptureMode: CaptureMode.photo,
            photoPathBuilder: (sensors) async {
              final Directory extDir = await getTemporaryDirectory();
              final testDir = await Directory(
                '${extDir.path}/camerawesome',
              ).create(recursive: true);
              if (sensors.length == 1) {
                final String filePath =
                    '${testDir.path}/${DateTime.now().millisecondsSinceEpoch}.jpg';
                return SingleCaptureRequest(filePath, sensors.first);
              }
              // Separate pictures taken with front and back camera
              return MultipleCaptureRequest(
                {
                  for (final sensor in sensors)
                    sensor:
                        '${testDir.path}/${sensor.position == SensorPosition.front ? 'front_' : "back_"}${DateTime.now().millisecondsSinceEpoch}.jpg',
                },
              );
            },
            videoOptions: VideoOptions(
              enableAudio: true,
              ios: CupertinoVideoOptions(
                fps: 10,
              ),
              android: AndroidVideoOptions(
                bitrate: 6000000,
                fallbackStrategy: QualityFallbackStrategy.lower,
              ),
            ),
            exifPreferences: ExifPreferences(saveGPSLocation: true),
          ),
          sensorConfig: SensorConfig.single(
            sensor: Sensor.position(SensorPosition.back),
            flashMode: FlashMode.auto,
            aspectRatio: CameraAspectRatios.ratio_4_3,
            zoom: 0.0,
          ),
          enablePhysicalButton: true,
          // filter: AwesomeFilter.AddictiveRed,
          previewAlignment: Alignment.center,
          previewFit: CameraPreviewFit.contain,
          // Add manual exposure control to the default UI
          // The middle content will only contain the blur toggle and a spacer
          // Middle content is just a spacer now
          middleContentBuilder: (state) {
            return Column(
              mainAxisAlignment: MainAxisAlignment.end, // Align to bottom
              mainAxisSize: MainAxisSize.min, // Occupy minimum space
              children: [
                // ISO slider panel (moved here)
                ValueListenableBuilder<bool>(
                  valueListenable: _showISOSlider,
                  builder: (context, isVisible, child) {
                    return AnimatedContainer(
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                      height: isVisible ? 450 : 0, // Further adjusted height to prevent overflow based on feedback
                      child: isVisible ? AwesomeISOSelector(
                        state: state,
                        showButton: false, // Only show the panel
                        visibilityNotifier: _showISOSlider,
                        showResetButton: true,
                        sliderActiveColor: Colors.yellow,
                        sliderInactiveColor: Colors.yellow.withOpacity(0.3),
                        textColor: Colors.white,
                      ) : null,
                    );
                  },
                ),
                // White Balance slider panel
                AwesomeWhiteBalanceSelector(
                  state: state,
                  showButton: false, // Only show the panel
                  visibilityNotifier: _showWhiteBalanceSlider,
                  showResetButton: true,
                  sliderActiveColor: Colors.cyan,
                  sliderInactiveColor: Colors.cyan.withOpacity(0.3),
                  textColor: Colors.white,
                ),
                AwesomeZoomSelector(state: state),
              ],
            );
          },
          // Top actions will contain all the buttons
          topActionsBuilder: (state) {
            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16.0),
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly, // Equally space all icons
                  children: [
                    // All icons in a single row
                    awesome_flash_button.AwesomeFlashButton(state: state),
                    AwesomeShutterSpeedSelector(
                      state: state,
                      showButton: true,
                      showResetButton: false,
                      visibilityNotifier: _showShutterSpeedSlider,
                      sliderActiveColor: Colors.orange,
                      sliderInactiveColor: Colors.orange.withOpacity(0.3),
                      textColor: Colors.white,
                    ),
                    if (state is PhotoCameraState) // Conditional rendering
                      AwesomeAspectRatioButton(state: state as PhotoCameraState),
                    if (state is PhotoCameraState) // Conditional rendering
                      AwesomeLocationButton(state: state as PhotoCameraState),
                    AwesomeExposureSelector(
                      state: state,
                      showButton: true,
                      showResetButton: false,
                      visibilityNotifier: _showExposureSlider,
                      sliderActiveColor: Colors.white,
                      sliderInactiveColor: Colors.white.withOpacity(0.3),
                      textColor: Colors.white,
                    ),
                    if (state is PhotoCameraState)
                      RawMaterialButton(
                        onPressed: () {
                          _showBlurSlider.value = !_showBlurSlider.value;
                        },
                        elevation: 0.0,
                        fillColor: Colors.transparent,
                        padding: const EdgeInsets.all(15.0),
                        shape: const CircleBorder(),
                        child: const Icon(
                          Icons.landscape,
                          size: 35.0,
                          color: Colors.white,
                        ),
                      ),
                    // White Balance Control button (re-added to top bar)
                    AwesomeWhiteBalanceSelector(
                      state: state,
                      showButton: true,
                      showResetButton: false,
                      visibilityNotifier: _showWhiteBalanceSlider,
                      sliderActiveColor: Colors.cyan,
                      sliderInactiveColor: Colors.cyan.withOpacity(0.3),
                      textColor: Colors.white,
                    ),
                    // ISO Control button
                    AwesomeISOSelector(
                      state: state,
                      showButton: true,
                      showResetButton: false,
                      visibilityNotifier: _showISOSlider,
                      sliderActiveColor: Colors.yellow,
                      sliderInactiveColor: Colors.yellow.withOpacity(0.3),
                      textColor: Colors.white,
                    ),
                  ],
                ),
              ),
            );
          },
          // Bottom actions will contain all the sliders and mode selectors, plus capture/switch buttons
          bottomActionsBuilder: (state) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch, // Stretch to fill width
              children: [
                // Shutter speed slider panel
                AwesomeShutterSpeedSelector(
                  state: state,
                  showButton: false, // Only show the panel
                  visibilityNotifier: _showShutterSpeedSlider,
                  showResetButton: true,
                  sliderActiveColor: Colors.orange,
                  sliderInactiveColor: Colors.orange.withOpacity(0.3),
                  textColor: Colors.white,
                ),
                // Exposure slider panel
                AwesomeExposureSelector(
                  state: state,
                  showButton: false, // Only show the panel
                  visibilityNotifier: _showExposureSlider,
                  showResetButton: true,
                  sliderActiveColor: Colors.white,
                  sliderInactiveColor: Colors.white.withOpacity(0.3),
                  textColor: Colors.white,
                ),
                // Blur slider panel
                if (state is PhotoCameraState)
                  ValueListenableBuilder<bool>(
                    valueListenable: _showBlurSlider,
                    builder: (context, isVisible, child) {
                      return AnimatedContainer(
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                        height: isVisible ? 120 : 0, // Approximate height of blur slider
                        child: isVisible ? AwesomeBlurSelector(state: state) : null,
                      );
                    },
                  ),
                // Main controls section
                Container(
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.6), // More opaque background
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(24.0),
                      topRight: Radius.circular(24.0),
                    ),
                  ),
                  padding: const EdgeInsets.only(top: 16.0, bottom: 24.0), // Increased vertical padding
                  child: Column(
                    children: [
                      // Camera Mode Selector
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 24.0),
                        child: AwesomeCameraModeSelector(state: state),
                      ),
                      const SizedBox(height: 24.0), // More spacing
                      // Capture and Switch Buttons Row
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          AwesomeCameraSwitchButton(state: state),
                          // Custom capture button that handles RepaintBoundary capture
                          GestureDetector(
                            onTap: () => handleCustomCapture(state),
                            child: Container(
                              width: 80,
                              height: 80,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: Colors.white,
                                border: Border.all(color: Colors.grey, width: 4),
                              ),
                              child: const Icon(
                                Icons.camera_alt,
                                size: 40,
                                color: Colors.black,
                              ),
                            ),
                          ),
                          ValueListenableBuilder<String?>(
                            valueListenable: _lastCapturedFilePathNotifier,
                            builder: (context, path, child) {
                              if (path == null) {
                                return const SizedBox(width: 50, height: 50); // Placeholder for alignment
                              }
                              return GestureDetector(
                                onTap: () {
                                  // Navigate to gallery screen
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => const GalleryScreen(),
                                    ),
                                  );
                                },
                                child: Container(
                                  width: 50,
                                  height: 50,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    border: Border.all(color: Colors.white, width: 2.0), // Add a white border
                                  ),
                                  child: ClipOval(
                                    child: Image.file(
                                      File(path),
                                      width: 50,
                                      height: 50,
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            );
          },
          onPreviewTapBuilder: (state) => OnPreviewTap(
            onTap: (position, flutterPreviewSize, pixelPreviewSize) {
              if (state is PhotoCameraState) {
                state.focusOnPoint(
                  flutterPosition: position,
                  pixelPreviewSize: pixelPreviewSize,
                  flutterPreviewSize: flutterPreviewSize,
                );
                // When tap-to-focus occurs, blur is reset, so hide all sliders
                _showBlurSlider.value = false;
                _showShutterSpeedSlider.value = false;
                _showExposureSlider.value = false;
              }
            },
          ),
          onMediaTap: (mediaCapture) {
            mediaCapture.captureRequest.when(
              single: (single) {
                debugPrint('single: ${single.file?.path}');
                single.file?.open();
              },
              multiple: (multiple) {
                multiple.fileBySensor.forEach((key, value) {
                  debugPrint('multiple file taken: $key ${value?.path}');
                  value?.open();
                });
              },
            );
          },
          availableFilters: awesomePresetFiltersList,
          ),
        ),
      ),
    );
  }
}
