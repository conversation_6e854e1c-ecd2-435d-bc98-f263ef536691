import 'dart:async';
import 'package:camerawesome/camerawesome_plugin.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// ISO preset values for professional photography
enum ISOValue {
  auto(-1.0, 'AUTO', Icons.auto_mode),
  iso100(100.0, '100', Icons.iso),
  iso200(200.0, '200', Icons.iso),
  iso400(400.0, '400', Icons.iso),
  iso800(800.0, '800', Icons.iso),
  iso1600(1600.0, '1600', Icons.iso),
  iso3200(3200.0, '3200', Icons.iso),
  iso6400(6400.0, '6400', Icons.iso);

  const ISOValue(this.value, this.label, this.icon);
  
  final double value;
  final String label;
  final IconData icon;

  static ISOValue fromValue(double value) {
    return ISOValue.values.firstWhere(
      (iso) => iso.value == value,
      orElse: () => ISOValue.auto,
    );
  }

  bool get isAuto => value == -1.0;
}

/// A professional ISO control panel for camera applications
/// 
/// Features:
/// - Auto/Manual ISO toggle
/// - Slider for ISO value selection (100-6400)
/// - Real-time ISO value display
/// - Smooth animations and haptic feedback
/// - Consistent styling with existing CamerAwesome selectors
class AwesomeISOSelector extends StatefulWidget {
  final CameraState state;
  final bool showResetButton;
  final Color? sliderActiveColor;
  final Color? sliderInactiveColor;
  final Color? textColor;
  final ValueNotifier<bool>? visibilityNotifier;
  final bool showButton;
  final EdgeInsets padding;
  final bool showLabel;

  const AwesomeISOSelector({
    super.key,
    required this.state,
    this.showResetButton = true,
    this.sliderActiveColor,
    this.sliderInactiveColor,
    this.textColor,
    this.visibilityNotifier,
    this.showButton = true,
    this.padding = const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
    this.showLabel = true,
  });

  @override
  State<AwesomeISOSelector> createState() => _AwesomeISOSelectorState();
}

class _AwesomeISOSelectorState extends State<AwesomeISOSelector> {
  int _currentIndex = 0; // Default to auto mode
  Timer? _debounceTimer;
  
  // Use a local ValueNotifier if none is provided externally
  late final ValueNotifier<bool> _internalVisibilityNotifier;

  @override
  void initState() {
    super.initState();
    
    // Initialize with current ISO from state
    final currentISO = widget.state.iso;
    _currentIndex = _getIndexFromISO(currentISO);

    _internalVisibilityNotifier = widget.visibilityNotifier ?? ValueNotifier(false);
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    // Only dispose the internal notifier if it was created by this widget
    if (widget.visibilityNotifier == null) {
      _internalVisibilityNotifier.dispose();
    }
    super.dispose();
  }

  int _getIndexFromISO(double isoValue) {
    final isoEnum = ISOValue.fromValue(isoValue);
    return ISOValue.values.indexOf(isoEnum);
  }

  void _onISOChanged(double sliderValue) {
    final newIndex = sliderValue.round();
    
    setState(() {
      _currentIndex = newIndex;
    });

    // Provide haptic feedback
    HapticFeedback.selectionClick();

    // Debounce the actual state update to prevent excessive processing
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 300), () {
      if (mounted) {
        final isoValue = ISOValue.values[_currentIndex];
        widget.state.setISO(isoValue.value);
      }
    });
  }

  void _resetToAuto() {
    setState(() {
      _currentIndex = 0; // Auto mode
    });
    widget.state.setISO(-1.0);
    HapticFeedback.lightImpact();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.showButton) {
      // Only show the panel
      return ValueListenableBuilder<bool>(
        valueListenable: _internalVisibilityNotifier,
        builder: (context, isVisible, child) {
          return AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            height: isVisible ? 120 : 0,
            child: isVisible ? _buildISOPanel() : null,
          );
        },
      );
    }

    // Show both button and panel
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // ISO Button
        _buildISOButton(),
        // ISO Panel
        ValueListenableBuilder<bool>(
          valueListenable: _internalVisibilityNotifier,
          builder: (context, isVisible, child) {
            return AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              height: isVisible ? 120 : 0,
              child: isVisible ? _buildISOPanel() : null,
            );
          },
        ),
      ],
    );
  }

  Widget _buildISOButton() {
    return StreamBuilder<double>(
      stream: widget.state.iso$,
      builder: (context, snapshot) {
        final currentISO = snapshot.data ?? -1.0;
        final isoValue = ISOValue.fromValue(currentISO);
        final theme = AwesomeThemeProvider.of(context).theme;
        final buttonTheme = theme.buttonTheme;

        return AwesomeOrientedWidget(
          rotateWithDevice: buttonTheme.rotateWithCamera,
          child: buttonTheme.buttonBuilder(
            AwesomeCircleWidget.icon(
              icon: Icons.iso,
              theme: theme,
            ),
            () {
              _internalVisibilityNotifier.value = !_internalVisibilityNotifier.value;
              HapticFeedback.selectionClick();
            },
          ),
        );
      },
    );
  }

  Widget _buildISOPanel() {
    return Card(
      elevation: 8,
      color: Colors.black.withOpacity(0.7),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (widget.showLabel)
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'ISO',
                    style: TextStyle(
                      color: widget.textColor ?? Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      shadows: [
                        Shadow(
                          blurRadius: 4.0,
                          color: Colors.black.withOpacity(0.5),
                          offset: const Offset(1.0, 1.0),
                        ),
                      ],
                    ),
                  ),
                  if (widget.showResetButton)
                    MaterialButton(
                      onPressed: _resetToAuto,
                      color: (widget.sliderActiveColor ?? Colors.yellow).withOpacity(0.2),
                      elevation: 0,
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      child: Text(
                        'AUTO',
                        style: TextStyle(
                          color: widget.textColor ?? Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                ],
              ),
            const SizedBox(height: 10),
            // ISO Value Display
            Text(
              ISOValue.values[_currentIndex].label,
              style: TextStyle(
                color: widget.textColor ?? Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
                shadows: [
                  Shadow(
                    blurRadius: 4.0,
                    color: Colors.black.withOpacity(0.5),
                    offset: const Offset(1.0, 1.0),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 8),
            // ISO Slider
            SliderTheme(
              data: SliderTheme.of(context).copyWith(
                activeTrackColor: widget.sliderActiveColor ?? Colors.yellow,
                inactiveTrackColor: widget.sliderInactiveColor ?? Colors.white.withOpacity(0.3),
                thumbColor: widget.sliderActiveColor ?? Colors.yellow,
                overlayColor: (widget.sliderActiveColor ?? Colors.yellow).withOpacity(0.2),
                trackHeight: 4,
                thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 10),
              ),
              child: Slider(
                value: _currentIndex.toDouble(),
                min: 0,
                max: (ISOValue.values.length - 1).toDouble(),
                divisions: ISOValue.values.length - 1,
                onChanged: _onISOChanged,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
