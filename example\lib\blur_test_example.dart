import 'package:camerawesome/camerawesome_plugin.dart';
import 'package:flutter/material.dart';

/// Example demonstrating the blur feature
class BlurTestExample extends StatelessWidget {
  const BlurTestExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Blur Test Example'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
      ),
      body: CameraAwesomeBuilder.awesome(
        saveConfig: SaveConfig.photoAndVideo(
          initialCaptureMode: CaptureMode.photo,
        ),
        sensorConfig: SensorConfig.single(
          flashMode: FlashMode.auto,
          aspectRatio: CameraAspectRatios.ratio_16_9,
        ),
        previewFit: CameraPreviewFit.fitWidth,
        onMediaTap: (mediaCapture) {
          // Handle media tap - could open gallery or preview
          debugPrint('Media captured: ${mediaCapture.captureRequest}');
        },
        // Custom layout to test blur functionality
        middleContentBuilder: (state) {
          return Column(
            children: [
              const Spacer(),
              // Test blur controls
              if (state is PhotoCameraState) ...[
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 20),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.black54,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    children: [
                      const Text(
                        'Blur Controls',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 12),
                      // Blur intensity display
                      StreamBuilder<double>(
                        stream: state.blur$,
                        builder: (context, snapshot) {
                          final intensity = snapshot.data ?? 0.0;
                          return Text(
                            'Blur Intensity: ${intensity.toStringAsFixed(1)}',
                            style: const TextStyle(
                              color: Colors.white70,
                              fontSize: 14,
                            ),
                          );
                        },
                      ),
                      const SizedBox(height: 8),
                      // Quick blur buttons
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          _BlurButton(
                            state: state,
                            label: 'Off',
                            intensity: 0.0,
                          ),
                          _BlurButton(
                            state: state,
                            label: 'Low',
                            intensity: 2.5,
                          ),
                          _BlurButton(
                            state: state,
                            label: 'Med',
                            intensity: 5.0,
                          ),
                          _BlurButton(
                            state: state,
                            label: 'High',
                            intensity: 7.5,
                          ),
                          _BlurButton(
                            state: state,
                            label: 'Max',
                            intensity: 10.0,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
              ],
              // Default camera mode selector
              AwesomeCameraModeSelector(state: state),
            ],
          );
        },
      ),
    );
  }
}

class _BlurButton extends StatelessWidget {
  final CameraState state;
  final String label;
  final double intensity;

  const _BlurButton({
    required this.state,
    required this.label,
    required this.intensity,
  });

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<double>(
      stream: state.blur$,
      builder: (context, snapshot) {
        final currentIntensity = snapshot.data ?? 0.0;
        final isActive = (currentIntensity - intensity).abs() < 0.1;
        
        return GestureDetector(
          onTap: () => state.setBlurIntensity(intensity),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: isActive ? Colors.blue : Colors.white24,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: isActive ? Colors.blue : Colors.white54,
                width: 1,
              ),
            ),
            child: Text(
              label,
              style: TextStyle(
                color: isActive ? Colors.white : Colors.white70,
                fontSize: 12,
                fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Performance test page to monitor blur performance
class BlurPerformanceTestPage extends StatefulWidget {
  const BlurPerformanceTestPage({super.key});

  @override
  State<BlurPerformanceTestPage> createState() => _BlurPerformanceTestPageState();
}

class _BlurPerformanceTestPageState extends State<BlurPerformanceTestPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Blur Performance Test'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
      ),
      body: CameraAwesomeBuilder.custom(
        builder: (cameraState, preview) {
          return cameraState.when(
            onPreparingCamera: (state) =>
                const Center(child: CircularProgressIndicator()),
            onPhotoMode: (state) => _PerformanceTestUI(state),
            onVideoMode: (state) => _PerformanceTestUI(state),
            onVideoRecordingMode: (state) => _PerformanceTestUI(state),
          );
        },
        saveConfig: SaveConfig.photoAndVideo(),
      ),
    );
  }
}

class _PerformanceTestUI extends StatelessWidget {
  final CameraState state;

  const _PerformanceTestUI(this.state);

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Camera preview (handled by CameraAwesome)
        Positioned.fill(child: Container()),
        
        // Performance overlay
        Positioned(
          top: 50,
          left: 20,
          right: 20,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.black87,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Performance Monitor',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 12),
                StreamBuilder<double>(
                  stream: state.blur$,
                  builder: (context, snapshot) {
                    final intensity = snapshot.data ?? 0.0;
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Blur Intensity: ${intensity.toStringAsFixed(1)}',
                          style: const TextStyle(color: Colors.white70),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Status: ${intensity > 0 ? "Blur Active" : "Blur Disabled"}',
                          style: TextStyle(
                            color: intensity > 0 ? Colors.green : Colors.grey,
                          ),
                        ),
                      ],
                    );
                  },
                ),
              ],
            ),
          ),
        ),
        
        // Bottom controls
        Positioned(
          bottom: 100,
          left: 0,
          right: 0,
          child: Container(
            color: Colors.black54,
            child: Column(
              children: [
                // Blur selector would be here if exported publicly
                AwesomeBottomActions(state: state),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
