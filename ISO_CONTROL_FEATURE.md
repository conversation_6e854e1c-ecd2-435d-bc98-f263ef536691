# 📸 ISO Control Feature

A professional ISO control feature has been added to the CamerAwesome Flutter plugin, allowing users to manually adjust ISO sensitivity for optimal exposure control.

## ✨ Key Features

### 🎛️ Modern UI Components
- **Slider Control**: Smooth, responsive slider for ISO adjustment
- **ISO Display**: Real-time ISO value display (AUTO, 100-6400)
- **Toggle Button**: Clean ISO icon button to show/hide controls
- **Reset Button**: One-tap "AUTO" button to return to auto-ISO

### 📱 Professional ISO Values
- **Auto Mode**: Automatic ISO adjustment by camera
- **Manual Values**: 100, 200, 400, 800, 1600, 3200, 6400
- **Device Limits**: Automatically clamped to device capabilities
- **Performance Optimized**: Debounced updates for smooth operation

### 🎨 UI Design
- **Dark Theme**: Consistent with app's dark UI theme
- **Soft Glow**: Professional iconography with gradient backgrounds
- **Smooth Animations**: 300ms transitions for show/hide
- **Haptic Feedback**: Tactile response on value changes

## 🚀 Usage

### Basic Usage

```dart
// Add to your camera UI
AwesomeISOSelector(
  state: cameraState,
  sliderActiveColor: Colors.yellow,
  sliderInactiveColor: Colors.yellow.withOpacity(0.3),
  textColor: Colors.white,
)
```

### Integration Example

```dart
CameraAwesomeBuilder.awesome(
  // ... other configuration
  topActionsBuilder: (state) => AwesomeTopActions(
    state: state,
    children: [
      AwesomeFlashButton(state: state),
      // Add ISO control to top bar
      AwesomeISOSelector(
        state: state,
        showButton: true,
        showResetButton: false,
        visibilityNotifier: _showISOSlider,
        sliderActiveColor: Colors.yellow,
        sliderInactiveColor: Colors.yellow.withOpacity(0.3),
        textColor: Colors.white,
      ),
      const Spacer(),
      AwesomeCameraSwitchButton(state: state),
    ],
  ),
  bottomActionsBuilder: (state) => Column(
    children: [
      // Add ISO slider panel
      AwesomeISOSelector(
        state: state,
        showButton: false, // Only show the panel
        visibilityNotifier: _showISOSlider,
        showResetButton: true,
        sliderActiveColor: Colors.yellow,
        sliderInactiveColor: Colors.yellow.withOpacity(0.3),
        textColor: Colors.white,
      ),
      // ... other bottom actions
    ],
  ),
)
```

### Programmatic Control

```dart
// Set manual ISO
await cameraState.setISO(800.0);

// Set auto ISO
await cameraState.setISO(-1.0);

// Listen to ISO changes
StreamBuilder<double>(
  stream: cameraState.iso$,
  builder: (context, snapshot) {
    final currentISO = snapshot.data ?? -1.0;
    final isAuto = currentISO == -1.0;
    return Text(isAuto ? 'AUTO' : 'ISO ${currentISO.toInt()}');
  },
)
```

## 🔧 API Reference

### AwesomeISOSelector Properties

| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `state` | `CameraState` | required | Camera state instance |
| `showResetButton` | `bool` | `true` | Show AUTO reset button |
| `sliderActiveColor` | `Color?` | `null` | Active slider color |
| `sliderInactiveColor` | `Color?` | `null` | Inactive slider color |
| `textColor` | `Color?` | `null` | Text color |
| `visibilityNotifier` | `ValueNotifier<bool>?` | `null` | Control visibility |
| `showButton` | `bool` | `true` | Show icon button |
| `showLabel` | `bool` | `true` | Show ISO label |

### CameraState Methods

```dart
// Set ISO value
Future<void> setISO(double isoValue)

// Get current ISO
double get iso

// Listen to ISO changes
Stream<double> get iso$
```

### ISO Values

```dart
enum ISOValue {
  auto(-1.0, 'AUTO'),     // Automatic ISO
  iso100(100.0, '100'),   // ISO 100
  iso200(200.0, '200'),   // ISO 200
  iso400(400.0, '400'),   // ISO 400
  iso800(800.0, '800'),   // ISO 800
  iso1600(1600.0, '1600'), // ISO 1600
  iso3200(3200.0, '3200'), // ISO 3200
  iso6400(6400.0, '6400'), // ISO 6400
}
```

## 🏗️ Architecture

### State Management
- **BehaviorSubject**: Reactive ISO value management
- **Stream-based**: Real-time UI updates
- **Debounced**: Performance-optimized updates

### Native Implementation
- **iOS**: AVCaptureDevice custom exposure mode
- **Android**: CameraX Camera2 interop with manual sensitivity
- **Auto Mode**: Continuous auto exposure on both platforms

### UI Pattern
- **Consistent**: Follows existing camera control patterns
- **Modular**: Reusable component design
- **Accessible**: Proper theming and accessibility support

## 🎯 Performance

- **30+ FPS**: Maintains smooth camera preview
- **Debounced Updates**: 300ms debounce for optimal performance
- **Memory Efficient**: Proper stream disposal and lifecycle management
- **Device Optimized**: Automatic clamping to device capabilities

## 📱 Platform Support

- ✅ **iOS**: Full support with AVCaptureDevice
- ✅ **Android**: Full support with CameraX
- ✅ **Auto Mode**: Seamless fallback on both platforms
- ✅ **Device Limits**: Automatic capability detection

## 🔄 Integration with Existing Features

The ISO control integrates seamlessly with:
- **Exposure Control**: Works alongside manual exposure
- **Shutter Speed**: Complements manual shutter speed
- **White Balance**: Part of complete manual control suite
- **Flash Control**: Maintains compatibility with flash modes

## 🎨 Customization

The ISO control supports full theming customization:
- Custom colors for active/inactive states
- Configurable text colors
- Adjustable padding and sizing
- Optional labels and reset buttons

---

**Note**: The ISO control feature maintains the established UI patterns and performance standards of the CamerAwesome plugin while providing professional-grade manual camera control capabilities.
