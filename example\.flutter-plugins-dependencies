{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "camerawesome", "path": "C:\\\\Users\\\\<USER>\\\\Documents\\\\Falah\\\\Apps\\\\Knff-cams\\\\knff-beta\\\\OG\\\\CamerAwesome\\\\", "native_build": true, "dependencies": []}, {"name": "gal", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\gal-2.3.1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "google_mlkit_barcode_scanning", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_barcode_scanning-0.14.1\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"]}, {"name": "google_mlkit_commons", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_commons-0.11.0\\\\", "native_build": true, "dependencies": []}, {"name": "google_mlkit_face_detection", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_face_detection-0.13.1\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"]}, {"name": "google_mlkit_text_recognition", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_text_recognition-0.15.0\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"]}, {"name": "open_filex", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\open_filex-4.7.0\\\\", "native_build": true, "dependencies": []}, {"name": "path_provider_foundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_foundation-2.4.0\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "patrol", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\patrol-3.14.0\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "permission_handler_apple", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\permission_handler_apple-9.4.7\\\\", "native_build": true, "dependencies": []}, {"name": "video_player_avfoundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\video_player_avfoundation-2.6.1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}], "android": [{"name": "camerawesome", "path": "C:\\\\Users\\\\<USER>\\\\Documents\\\\Falah\\\\Apps\\\\Knff-cams\\\\knff-beta\\\\OG\\\\CamerAwesome\\\\", "native_build": true, "dependencies": []}, {"name": "gal", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\gal-2.3.1\\\\", "native_build": true, "dependencies": []}, {"name": "google_mlkit_barcode_scanning", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_barcode_scanning-0.14.1\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"]}, {"name": "google_mlkit_commons", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_commons-0.11.0\\\\", "native_build": true, "dependencies": []}, {"name": "google_mlkit_face_detection", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_face_detection-0.13.1\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"]}, {"name": "google_mlkit_text_recognition", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\google_mlkit_text_recognition-0.15.0\\\\", "native_build": true, "dependencies": ["google_mlkit_commons"]}, {"name": "open_filex", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\open_filex-4.7.0\\\\", "native_build": true, "dependencies": []}, {"name": "path_provider_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_android-2.2.9\\\\", "native_build": true, "dependencies": []}, {"name": "patrol", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\patrol-3.14.0\\\\", "native_build": true, "dependencies": []}, {"name": "permission_handler_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\permission_handler_android-12.1.0\\\\", "native_build": true, "dependencies": []}, {"name": "video_player_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\video_player_android-2.6.0\\\\", "native_build": true, "dependencies": []}], "macos": [{"name": "gal", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\gal-2.3.1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "path_provider_foundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_foundation-2.4.0\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "patrol", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\patrol-3.14.0\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "video_player_avfoundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\video_player_avfoundation-2.6.1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": []}], "linux": [{"name": "path_provider_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_linux-2.2.1\\\\", "native_build": false, "dependencies": []}], "windows": [{"name": "gal", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\gal-2.3.1\\\\", "native_build": true, "dependencies": []}, {"name": "path_provider_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_windows-2.3.0\\\\", "native_build": false, "dependencies": []}, {"name": "permission_handler_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\permission_handler_windows-0.2.1\\\\", "native_build": true, "dependencies": []}], "web": [{"name": "permission_handler_html", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\permission_handler_html-0.1.3+5\\\\", "dependencies": []}, {"name": "video_player_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\video_player_web-2.3.2\\\\", "dependencies": []}]}, "dependencyGraph": [{"name": "camerawesome", "dependencies": ["path_provider"]}, {"name": "gal", "dependencies": []}, {"name": "google_mlkit_barcode_scanning", "dependencies": ["google_mlkit_commons"]}, {"name": "google_mlkit_commons", "dependencies": []}, {"name": "google_mlkit_face_detection", "dependencies": ["google_mlkit_commons"]}, {"name": "google_mlkit_text_recognition", "dependencies": ["google_mlkit_commons"]}, {"name": "open_filex", "dependencies": []}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "patrol", "dependencies": []}, {"name": "permission_handler", "dependencies": ["permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_windows"]}, {"name": "permission_handler_android", "dependencies": []}, {"name": "permission_handler_apple", "dependencies": []}, {"name": "permission_handler_html", "dependencies": []}, {"name": "permission_handler_windows", "dependencies": []}, {"name": "video_player", "dependencies": ["video_player_android", "video_player_avfoundation", "video_player_web"]}, {"name": "video_player_android", "dependencies": []}, {"name": "video_player_avfoundation", "dependencies": []}, {"name": "video_player_web", "dependencies": []}], "date_created": "2025-07-27 13:50:47.543546", "version": "3.24.3", "swift_package_manager_enabled": false}