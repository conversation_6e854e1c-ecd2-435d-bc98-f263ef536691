import 'package:flutter/material.dart';
import 'package:camerawesome/camerawesome_plugin.dart';
import '../theme/app_theme.dart';
import 'apple_style_button.dart';
import 'apple_style_control_panel.dart';
import 'apple_style_slider.dart';

/// Apple-style top camera controls with clean layout
class AppleCameraTopControls extends StatelessWidget {
  final CameraState state;
  final ValueNotifier<bool> showISOSlider;
  final ValueNotifier<bool> showWhiteBalanceSlider;
  final ValueNotifier<bool> showExposureSlider;
  final ValueNotifier<bool> showShutterSpeedSlider;
  final ValueNotifier<bool> showBlurSlider;

  const AppleCameraTopControls({
    super.key,
    required this.state,
    required this.showISOSlider,
    required this.showWhiteBalanceSlider,
    required this.showExposureSlider,
    required this.showShutterSpeedSlider,
    required this.showBlurSlider,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacing16,
        vertical: AppTheme.spacing12,
      ),
      child: SafeArea(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Left side controls
            Row(
              children: [
                // Flash control
                AppleStyleToggleButton(
                  activeChild: const Icon(Icons.flash_on),
                  inactiveChild: const Icon(Icons.flash_off),
                  isActive: state.sensorConfig.flashMode != FlashMode.none,
                  activeColor: AppTheme.flashControlColor,
                  onChanged: (isActive) {
                    state.sensorConfig.setFlashMode(
                      isActive ? FlashMode.auto : FlashMode.none,
                    );
                  },
                  tooltip: 'Flash',
                ),
                const SizedBox(width: AppTheme.spacing12),
                
                // Aspect ratio (photo mode only)
                if (state is PhotoCameraState)
                  AppleStyleButton(
                    onTap: () {
                      // Cycle through aspect ratios
                      final currentRatio = state.sensorConfig.aspectRatio;
                      CameraAspectRatios newRatio;
                      switch (currentRatio) {
                        case CameraAspectRatios.ratio_4_3:
                          newRatio = CameraAspectRatios.ratio_16_9;
                          break;
                        case CameraAspectRatios.ratio_16_9:
                          newRatio = CameraAspectRatios.ratio_1_1;
                          break;
                        default:
                          newRatio = CameraAspectRatios.ratio_4_3;
                      }
                      state.sensorConfig.setAspectRatio(newRatio);
                    },
                    tooltip: 'Aspect Ratio',
                    child: _getAspectRatioIcon(state.sensorConfig.aspectRatio),
                  ),
              ],
            ),

            // Right side controls
            Row(
              children: [
                // Manual controls toggle
                AppleStyleButton(
                  onTap: () => _toggleManualControls(),
                  isActive: _isAnyControlVisible(),
                  tooltip: 'Manual Controls',
                  child: const Icon(Icons.tune),
                ),
                const SizedBox(width: AppTheme.spacing12),
                
                // Settings/More options
                AppleStyleButton(
                  onTap: () => _showMoreOptions(context),
                  tooltip: 'More Options',
                  child: const Icon(Icons.more_horiz),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _getAspectRatioIcon(CameraAspectRatios ratio) {
    switch (ratio) {
      case CameraAspectRatios.ratio_16_9:
        return const Icon(Icons.crop_16_9);
      case CameraAspectRatios.ratio_1_1:
        return const Icon(Icons.crop_square);
      default:
        return const Icon(Icons.crop_7_5);
    }
  }

  bool _isAnyControlVisible() {
    return showISOSlider.value ||
           showWhiteBalanceSlider.value ||
           showExposureSlider.value ||
           showShutterSpeedSlider.value ||
           showBlurSlider.value;
  }

  void _toggleManualControls() {
    final isAnyVisible = _isAnyControlVisible();
    showISOSlider.value = false;
    showWhiteBalanceSlider.value = false;
    showExposureSlider.value = false;
    showShutterSpeedSlider.value = false;
    showBlurSlider.value = false;

    if (!isAnyVisible) {
      // Show the most commonly used control
      showExposureSlider.value = true;
    }
  }

  void _showMoreOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => AppleStyleMoreOptionsSheet(
        state: state,
        showISOSlider: showISOSlider,
        showWhiteBalanceSlider: showWhiteBalanceSlider,
        showExposureSlider: showExposureSlider,
        showShutterSpeedSlider: showShutterSpeedSlider,
        showBlurSlider: showBlurSlider,
      ),
    );
  }
}

/// Apple-style more options bottom sheet
class AppleStyleMoreOptionsSheet extends StatelessWidget {
  final CameraState state;
  final ValueNotifier<bool> showISOSlider;
  final ValueNotifier<bool> showWhiteBalanceSlider;
  final ValueNotifier<bool> showExposureSlider;
  final ValueNotifier<bool> showShutterSpeedSlider;
  final ValueNotifier<bool> showBlurSlider;

  const AppleStyleMoreOptionsSheet({
    super.key,
    required this.state,
    required this.showISOSlider,
    required this.showWhiteBalanceSlider,
    required this.showExposureSlider,
    required this.showShutterSpeedSlider,
    required this.showBlurSlider,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.darkGlassBackgroundStrong,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(AppTheme.radiusLarge),
          topRight: Radius.circular(AppTheme.radiusLarge),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: AppTheme.spacing12),
            decoration: BoxDecoration(
              color: AppTheme.systemGray3,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Title
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppTheme.spacing20),
            child: Row(
              children: [
                const Text(
                  'Camera Controls',
                  style: AppTheme.headlineMedium,
                ),
                const Spacer(),
                AppleStyleButton(
                  onTap: () => Navigator.pop(context),
                  size: 32,
                  child: const Icon(Icons.close, size: 18),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: AppTheme.spacing20),
          
          // Control options
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppTheme.spacing20),
            child: Column(
              children: [
                _buildControlOption(
                  'Exposure',
                  Icons.exposure,
                  AppTheme.exposureControlColor,
                  showExposureSlider,
                ),
                _buildControlOption(
                  'ISO',
                  Icons.iso,
                  AppTheme.isoControlColor,
                  showISOSlider,
                ),
                _buildControlOption(
                  'White Balance',
                  Icons.wb_auto,
                  AppTheme.whiteBalanceControlColor,
                  showWhiteBalanceSlider,
                ),
                _buildControlOption(
                  'Shutter Speed',
                  Icons.shutter_speed,
                  AppTheme.shutterSpeedControlColor,
                  showShutterSpeedSlider,
                ),
                if (state is PhotoCameraState)
                  _buildControlOption(
                    'Blur',
                    Icons.blur_on,
                    AppTheme.accentPurple,
                    showBlurSlider,
                  ),
              ],
            ),
          ),
          
          const SizedBox(height: AppTheme.spacing32),
        ],
      ),
    );
  }

  Widget _buildControlOption(
    String title,
    IconData icon,
    Color color,
    ValueNotifier<bool> notifier,
  ) {
    return ValueListenableBuilder<bool>(
      valueListenable: notifier,
      builder: (context, isVisible, child) {
        return Padding(
          padding: const EdgeInsets.only(bottom: AppTheme.spacing12),
          child: AppleStyleButton(
            onTap: () {
              // Hide all other controls first
              showISOSlider.value = false;
              showWhiteBalanceSlider.value = false;
              showExposureSlider.value = false;
              showShutterSpeedSlider.value = false;
              showBlurSlider.value = false;
              
              // Toggle this control
              notifier.value = !isVisible;
              Navigator.pop(context);
            },
            backgroundColor: isVisible ? color : AppTheme.systemGray.withOpacity(0.3),
            isCircular: false,
            padding: const EdgeInsets.all(AppTheme.spacing16),
            child: Row(
              children: [
                Icon(icon, color: isVisible ? AppTheme.primaryWhite : AppTheme.systemGray),
                const SizedBox(width: AppTheme.spacing12),
                Text(
                  title,
                  style: AppTheme.bodyMedium.copyWith(
                    color: isVisible ? AppTheme.primaryWhite : AppTheme.systemGray,
                  ),
                ),
                const Spacer(),
                if (isVisible)
                  const Icon(
                    Icons.check,
                    color: AppTheme.primaryWhite,
                    size: 20,
                  ),
              ],
            ),
          ),
        );
      },
    );
  }
}
