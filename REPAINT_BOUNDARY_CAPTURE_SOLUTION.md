# RepaintBoundary Capture Solution for Shutter Speed Effects

## Problem
The shutter speed effects (motion blur, light trails, brightness adjustments) are applied as UI overlays on the camera preview using Flutter widgets. However, when capturing photos, the native camera capture process only saves the raw camera output without these UI effects, resulting in captured images that don't match what the user sees in the preview.

## Solution Overview
We implemented a RepaintBoundary-based capture system that:
1. **Temporarily hides all UI sliders** during capture to ensure clean preview area
2. **Captures the entire preview widget tree** (including all effects) as an image
3. **Crops to extract only the camera preview area** (excluding fixed UI elements)
4. **Restores UI sliders** after capture completion
5. **Saves the result as a PNG file** with full preview height and effects preserved

This ensures that the saved image exactly matches what the user sees in the preview, with maximum image height and zero UI elements.

## Implementation Details

### 1. RepaintBoundary Wrapper
The entire CameraAwesome widget is wrapped in a RepaintBoundary with a GlobalKey:

```dart
RepaintBoundary(
  key: _preview<PERSON>ontainer<PERSON><PERSON>,
  child: CameraAwesomeBuilder.awesome(
    // ... camera configuration
  ),
)
```

**Note**: While this captures the entire screen, the implementation includes intelligent cropping to extract only the camera preview area, excluding UI controls.

### 2. Capture Methods

#### `_hideAllSliders()` Method
Temporarily hides all visible sliders and returns their previous states:

```dart
Map<String, bool> _hideAllSliders() {
  // Store current visibility states
  final currentStates = {
    'shutterSpeed': _showShutterSpeedSlider.value,
    'exposure': _showExposureSlider.value,
    'blur': _showBlurSlider.value,
    'iso': _showISOSlider.value,
    'whiteBalance': _showWhiteBalanceSlider.value,
  };

  // Hide all sliders
  _showShutterSpeedSlider.value = false;
  _showExposureSlider.value = false;
  _showBlurSlider.value = false;
  _showISOSlider.value = false;
  _showWhiteBalanceSlider.value = false;

  return currentStates;
}
```

#### `_restoreSliders()` Method
Restores slider visibility to their previous states:

```dart
void _restoreSliders(Map<String, bool> previousStates) {
  _showShutterSpeedSlider.value = previousStates['shutterSpeed'] ?? false;
  _showExposureSlider.value = previousStates['exposure'] ?? false;
  _showBlurSlider.value = previousStates['blur'] ?? false;
  _showISOSlider.value = previousStates['iso'] ?? false;
  _showWhiteBalanceSlider.value = previousStates['whiteBalance'] ?? false;
}
```

#### `_calculatePreviewBounds()` Method
Dynamically calculates the exact bounds of the camera preview area within the captured screen, accounting for all visible UI elements:

```dart
Rect? _calculatePreviewBounds(Size capturedImageSize) {
  try {
    // Get the screen size
    final screenSize = MediaQuery.of(context).size;
    final screenPadding = MediaQuery.of(context).padding;

    // Calculate TOP UI elements height dynamically
    double topControlsHeight = screenPadding.top + 80; // Status bar + top actions bar

    // Add middle content heights (ISO and White Balance sliders)
    if (_showISOSlider.value) {
      topControlsHeight += 450; // ISO slider height
    }

    if (_showWhiteBalanceSlider.value) {
      topControlsHeight += 120; // White balance selector height
    }

    topControlsHeight += 40; // Zoom selector height

    // Calculate BOTTOM UI elements height dynamically
    double bottomControlsHeight = 0;

    // Add heights of visible bottom sliders
    if (_showShutterSpeedSlider.value) {
      bottomControlsHeight += 120; // Shutter speed slider height
    }

    if (_showExposureSlider.value) {
      bottomControlsHeight += 120; // Exposure slider height
    }

    if (_showBlurSlider.value) {
      bottomControlsHeight += 120; // Blur slider height
    }

    // Main controls section (camera mode selector + capture buttons)
    bottomControlsHeight += 200; // Main controls section height

    // Add extra padding to ensure complete exclusion of UI elements
    const extraPadding = 20;
    topControlsHeight += extraPadding;
    bottomControlsHeight += extraPadding;

    // Available preview area
    final availableHeight = screenSize.height - topControlsHeight - bottomControlsHeight;
    final availableWidth = screenSize.width;

    // Convert to captured image coordinates (accounting for pixel ratio)
    const pixelRatio = 2.0;
    final scaledTop = topControlsHeight * pixelRatio;
    final scaledLeft = 0.0 * pixelRatio;
    final scaledWidth = availableWidth * pixelRatio;
    final scaledHeight = availableHeight * pixelRatio;

    return Rect.fromLTWH(scaledLeft, scaledTop, scaledWidth, scaledHeight);
  } catch (e) {
    debugPrint('Error calculating preview bounds: $e');
    return null;
  }
}
```

**Key Features:**
- **Dynamic UI Detection**: Checks visibility state of all sliders and UI elements
- **Accurate Heights**: Uses exact heights from widget definitions (120px for most sliders, 450px for ISO)
- **Safety Padding**: Adds extra padding to ensure complete UI exclusion
- **Debug Logging**: Provides detailed logging for troubleshooting bounds calculation

#### `_cropImageToPreview()` Method
Crops the captured full-screen image to show only the camera preview area:

```dart
Future<Uint8List?> _cropImageToPreview(Uint8List imageBytes) async {
  try {
    // Decode the image
    final ui.Codec codec = await ui.instantiateImageCodec(imageBytes);
    final ui.FrameInfo frameInfo = await codec.getNextFrame();
    final ui.Image originalImage = frameInfo.image;

    final imageSize = Size(originalImage.width.toDouble(), originalImage.height.toDouble());
    final previewBounds = _calculatePreviewBounds(imageSize);

    if (previewBounds == null) {
      return imageBytes; // Return original if bounds calculation fails
    }

    // Create a new canvas for the cropped image
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);

    // Draw only the preview area
    canvas.drawImageRect(
      originalImage,
      previewBounds,
      Rect.fromLTWH(0, 0, previewBounds.width, previewBounds.height),
      Paint(),
    );

    // Convert to image
    final picture = recorder.endRecording();
    final croppedImage = await picture.toImage(
      previewBounds.width.toInt(),
      previewBounds.height.toInt(),
    );

    // Convert to bytes
    final ByteData? byteData = await croppedImage.toByteData(format: ui.ImageByteFormat.png);
    return byteData?.buffer.asUint8List();
  } catch (e) {
    debugPrint('Error cropping image: $e');
    return imageBytes; // Return original if cropping fails
  }
}
```

#### `captureFilteredPreview()` Method
```dart
Future<Uint8List?> captureFilteredPreview() async {
  try {
    // Wait a frame to ensure the RepaintBoundary is properly rendered
    await Future.delayed(const Duration(milliseconds: 100));

    RenderRepaintBoundary? boundary = _previewContainerKey.currentContext
        ?.findRenderObject() as RenderRepaintBoundary?;

    if (boundary == null) {
      debugPrint('RepaintBoundary not found');
      return null;
    }

    // Capture the boundary as an image with high resolution
    ui.Image image = await boundary.toImage(pixelRatio: 2.0);
    ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);

    if (byteData == null) {
      debugPrint('Failed to convert image to bytes');
      return null;
    }

    debugPrint('Successfully captured RepaintBoundary: ${byteData.lengthInBytes} bytes');

    // Crop the image to show only the preview area
    final croppedImageBytes = await _cropImageToPreview(byteData.buffer.asUint8List());

    return croppedImageBytes;
  } catch (e) {
    debugPrint('Error capturing filtered preview: $e');
    return null;
  }
}
```

#### `saveFilteredImage()` Method
```dart
Future<String?> saveFilteredImage(Uint8List bytes) async {
  try {
    final Directory extDir = await getTemporaryDirectory();
    final testDir = await Directory(
      '${extDir.path}/camerawesome',
    ).create(recursive: true);
    
    final String filePath = 
        '${testDir.path}/filtered_${DateTime.now().millisecondsSinceEpoch}.png';
    
    final file = File(filePath);
    await file.writeAsBytes(bytes);
    
    debugPrint('Filtered image saved: $filePath');
    return filePath;
  } catch (e) {
    debugPrint('Error saving filtered image: $e');
    return null;
  }
}
```

### 3. Smart Capture Logic

#### `handleCustomCapture()` Method
Enhanced capture logic that temporarily hides UI elements for clean capture:
```dart
Future<void> handleCustomCapture(CameraState state) async {
  if (state is! PhotoCameraState) return;

  // Check if shutter speed effects are active
  final shutterSpeed = state.shutterSpeed;
  final hasShutterEffects = shutterSpeed > 0; // Manual shutter speed mode

  // Also check for other effects that might be active
  final blurIntensity = state.blurIntensity;
  final hasBlurEffects = blurIntensity > 0;

  if (hasShutterEffects || hasBlurEffects) {
    debugPrint('Effects detected - using RepaintBoundary capture');

    // Step 1: Hide all sliders temporarily
    final previousSliderStates = _hideAllSliders();

    try {
      // Step 2: Wait for UI to update (sliders to hide)
      await Future.delayed(const Duration(milliseconds: 350));

      // Step 3: Capture the preview with effects (now without visible sliders)
      final imageBytes = await captureFilteredPreview();
      if (imageBytes != null) {
        final savedPath = await saveFilteredImage(imageBytes);
        if (savedPath != null) {
          _lastCapturedFilePathNotifier.value = savedPath;
          debugPrint('Custom capture with effects completed: $savedPath');
          return;
        }
      }
      debugPrint('Failed to capture with effects, falling back to normal capture');
    } finally {
      // Step 4: Always restore sliders after capture (success or failure)
      _restoreSliders(previousSliderStates);
    }
  }

  // Fallback to normal capture
  state.takePhoto();
}
```

**Key Features:**
- **Temporary UI Hiding**: Hides all sliders before capture for maximum preview area
- **Animation Timing**: Waits 350ms for slider hide animations to complete
- **Guaranteed Restoration**: Uses try-finally to ensure sliders are always restored
- **Full Height Capture**: Maintains maximum image height without cropping around sliders

### 4. Custom Capture Button
The default AwesomeCaptureButton is replaced with a custom GestureDetector that calls the RepaintBoundary capture logic:

```dart
GestureDetector(
  onTap: () => handleCustomCapture(state),
  child: Container(
    width: 80,
    height: 80,
    decoration: BoxDecoration(
      shape: BoxShape.circle,
      color: Colors.white,
      border: Border.all(color: Colors.grey, width: 4),
    ),
    child: const Icon(
      Icons.camera_alt,
      size: 40,
      color: Colors.black,
    ),
  ),
),
```

## Key Features

### 1. Conditional Capture
- **With Effects**: When manual shutter speed is active (shutterSpeed > 0), uses RepaintBoundary capture
- **Without Effects**: When in auto mode (shutterSpeed = -1), falls back to normal camera capture for better performance and quality

### 2. High Resolution Capture
- Uses `pixelRatio: 2.0` for high-resolution capture
- Saves as PNG format to preserve quality and transparency

### 3. Error Handling
- Comprehensive error handling with fallback to normal capture
- Debug logging for troubleshooting

### 4. Performance Considerations
- Only activates RepaintBoundary capture when effects are actually being used
- Maintains normal camera performance when effects are disabled

## Benefits

1. **Visual Consistency**: Captured images exactly match the preview
2. **Effect Preservation**: All shutter speed effects (motion blur, light trails, brightness) are preserved
3. **Fallback Safety**: Automatically falls back to normal capture if RepaintBoundary capture fails
4. **Performance Optimized**: Only uses RepaintBoundary when effects are active

## Limitations

1. **File Format**: Saves as PNG instead of JPEG (larger file size but better quality)
2. **Resolution**: Limited to the preview resolution rather than full camera resolution
3. **Performance**: Slightly slower than native capture due to widget rendering

## Testing

Multiple test implementations are provided:

### 1. Basic RepaintBoundary Test
- **File**: `example/lib/test_repaint_boundary.dart`
- **Purpose**: Verify basic RepaintBoundary capture functionality
- **Run**: `flutter run lib/test_main.dart`

### 2. Camera Effects Demo
- **File**: `example/lib/repaint_boundary_demo.dart`
- **Purpose**: Simulate camera effects and test capture logic
- **Run**: `flutter run lib/demo_main.dart`
- **Features**:
  - Simulated shutter speed effects
  - Blur effects
  - Motion blur animation
  - Smart capture logic testing

### 3. Image Cropping Test
- **File**: `example/lib/cropping_test.dart`
- **Purpose**: Test the image cropping functionality that extracts only the preview area
- **Run**: `flutter run lib/cropping_main.dart`
- **Features**:
  - Simulated camera UI layout
  - Full-screen capture with RepaintBoundary
  - Automatic cropping to preview area only
  - Side-by-side comparison of original vs cropped images

### 4. Full Integration Test
- **File**: `example/lib/main.dart`
- **Purpose**: Complete camera app with RepaintBoundary capture
- **Run**: `flutter run` (requires physical device with camera)

## Usage Instructions

### For End Users
The solution is automatically integrated into the main camera app:

1. **Open the camera app**
2. **Enable manual shutter speed mode**:
   - Tap the shutter speed button in the top toolbar
   - Adjust the slider to set a manual shutter speed (> 0 seconds)
3. **Optionally enable blur effects**:
   - Tap the blur button (landscape icon)
   - Adjust blur intensity
4. **Observe effects in preview**:
   - Motion blur, light trails, and brightness adjustments appear in real-time
5. **Capture photo**:
   - Tap the capture button
   - The system automatically detects active effects
   - Uses RepaintBoundary capture to preserve effects
   - Shows feedback messages indicating capture method
6. **View results**:
   - Captured image includes all visible effects
   - Saved as PNG in the app's directory

### For Developers
To integrate this solution into other camera apps:

1. **Wrap your camera preview in RepaintBoundary**:
   ```dart
   RepaintBoundary(
     key: _previewContainerKey,
     child: YourCameraWidget(),
   )
   ```

2. **Implement capture methods**:
   - Copy `captureFilteredPreview()` method
   - Copy `saveFilteredImage()` method
   - Copy `handleCustomCapture()` method

3. **Replace capture button**:
   - Use custom capture button that calls `handleCustomCapture()`
   - Add effect detection logic

4. **Add user feedback**:
   - Show SnackBar messages for capture status
   - Indicate when effects are being captured

## Troubleshooting

### Common Issues

1. **RepaintBoundary not found**:
   - Ensure the GlobalKey is properly assigned
   - Check that the widget tree is fully built before capture
   - Add delay before capture if needed

2. **Capture returns null**:
   - Verify the RepaintBoundary contains renderable content
   - Check for proper widget mounting
   - Ensure effects are actually visible

3. **Poor capture quality**:
   - Increase `pixelRatio` parameter (default: 2.0)
   - Ensure preview is properly sized
   - Check device performance capabilities

4. **Performance issues**:
   - Only use RepaintBoundary capture when effects are active
   - Implement proper debouncing for effect changes
   - Consider reducing effect complexity on low-end devices

### Debug Tips

- Enable debug logging to track capture process
- Use the demo app to test capture logic without camera
- Check file system permissions for saving images
- Monitor memory usage during capture operations

This solution provides a seamless way to capture exactly what the user sees in the preview, solving the original problem of shutter speed effects not appearing in captured photos.
